# Floating UI System

Hệ thống quản lý floating UI chung cho toàn bộ dự án Vocab, cung cấp một cách thống nhất để quản lý các thành phần UI nổi như settings, guidance, notifications, tooltips, modals, và dropdowns.

## Tổng quan

Floating UI System bao gồm:
- **FloatingUIProvider**: Context provider quản lý state
- **FloatingUIManager**: Component render các floating elements
- **Hooks**: <PERSON><PERSON><PERSON> hooks để tương tác với floating elements
- **Types**: Type definitions cho TypeScript
- **Styles**: CSS styles cho animations và responsive design

## Cài đặt và Sử dụng

### 1. Setup trong Layout

```tsx
import { FloatingUIProvider, FloatingUIManager } from '@/components/floating-ui';

export default function RootLayout({ children }) {
  return (
    <FloatingUIProvider>
      {/* Your app content */}
      {children}
      
      {/* Floating UI Manager - renders all floating elements */}
      <FloatingUIManager />
    </FloatingUIProvider>
  );
}
```

### 2. Sử dụng Hooks

#### useFloatingUIElement (Hook chính)

```tsx
import { useFloatingUIElement } from '@/hooks/use-floating-ui';

function MyComponent() {
  const content = <div>My floating content</div>;
  
  const { show, hide, toggle, isVisible } = useFloatingUIElement(
    'my-element-id',
    content,
    {
      type: 'notification',
      priority: 'high',
      position: 'top-right',
      coordinates: { top: 16, right: 16 },
      animation: { type: 'slide', duration: 300 },
      autoShow: true,
    }
  );

  return (
    <button onClick={toggle}>
      {isVisible ? 'Hide' : 'Show'} Element
    </button>
  );
}
```

#### Specialized Hooks

```tsx
// Settings
const settings = useFloatingSettings('settings-id', {
  showLanguageSelector: true,
  showThemeSelector: true,
});

// Guidance
const guidance = useFloatingGuidance('guidance-id', {
  titleKey: 'guidance.title',
  steps: [
    { key: 'step1', icon: CheckIcon },
    { key: 'step2', icon: ArrowIcon },
  ],
  defaultOpen: true,
});

// Notification
const notification = useFloatingNotification(
  'notification-id',
  <div>Notification content</div>
);

// Tooltip
const tooltip = useFloatingTooltip(
  'tooltip-id',
  <div>Tooltip content</div>,
  { coordinates: { top: 100, left: 200 } }
);

// Modal
const modal = useFloatingModal(
  'modal-id',
  <div>Modal content</div>
);
```

### 3. Global Management

```tsx
import { useFloatingUIManager } from '@/hooks/use-floating-ui';

function GlobalControls() {
  const {
    hideAll,
    showAll,
    hideByType,
    showByType,
    visibleCount,
    totalCount,
  } = useFloatingUIManager();

  return (
    <div>
      <button onClick={hideAll}>Hide All</button>
      <button onClick={showAll}>Show All</button>
      <button onClick={() => hideByType('notification')}>
        Hide Notifications
      </button>
      <p>Visible: {visibleCount} / {totalCount}</p>
    </div>
  );
}
```

## Configuration

### FloatingUIConfig

```tsx
const config = {
  defaultZIndex: 1000,
  zIndexStep: 10,
  collisionMargin: 8,
  animationDefaults: {
    type: 'fade',
    duration: 200,
    delay: 0,
    easing: 'ease-in-out',
  },
  responsiveBreakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280,
  },
  positionDefaults: {
    'top-left': { top: 16, left: 16 },
    'top-right': { top: 16, right: 16 },
    'bottom-left': { bottom: 16, left: 16 },
    'bottom-right': { bottom: 16, right: 16 },
    'center': { top: 50, left: 50 },
  },
  priorityZIndex: {
    low: 1000,
    medium: 1100,
    high: 1200,
    critical: 1300,
  },
};

<FloatingUIProvider config={config}>
  {/* Your app */}
</FloatingUIProvider>
```

## Types

### FloatingUIType
- `'settings'` - Settings panels
- `'guidance'` - Page guidance/help
- `'notification'` - Notifications
- `'tooltip'` - Tooltips
- `'modal'` - Modals
- `'dropdown'` - Dropdowns
- `'custom'` - Custom elements

### FloatingPosition
- `'top-left'` | `'top-right'` | `'bottom-left'` | `'bottom-right'` | `'center'` | `'custom'`

### FloatingPriority
- `'low'` | `'medium'` | `'high'` | `'critical'`

### Animation Types
- `'fade'` - Fade in/out
- `'slide'` - Slide animation
- `'scale'` - Scale animation
- `'bounce'` - Bounce animation
- `'none'` - No animation

## Features

### 1. Collision Detection
Tự động phát hiện và xử lý collision giữa các floating elements:

```tsx
const element = useFloatingUIElement('id', content, {
  collisionDetection: true,
  onCollision: (collidingElements) => {
    console.log('Collision detected with:', collidingElements);
  },
});
```

### 2. Priority System
Elements với priority cao hơn sẽ hiển thị trên elements với priority thấp hơn:

```tsx
// Critical priority (z-index: 1300)
const modal = useFloatingModal('modal', content, { priority: 'critical' });

// High priority (z-index: 1200)
const settings = useFloatingSettings('settings', { priority: 'high' });
```

### 3. Responsive Design
Tự động điều chỉnh position và size cho mobile/tablet:

```tsx
const responsive = useResponsiveFloatingUI('id', content, {
  type: 'notification',
  mobileOptions: {
    coordinates: { top: 8, left: 8, right: 8 },
  },
  tabletOptions: {
    coordinates: { top: 16, right: 16 },
  },
  desktopOptions: {
    coordinates: { top: 24, right: 24 },
  },
});
```

### 4. Animation System
Hỗ trợ nhiều loại animation với cấu hình linh hoạt:

```tsx
const animated = useFloatingUIElement('id', content, {
  animation: {
    type: 'bounce',
    duration: 400,
    delay: 100,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },
});
```

### 5. Persistent Elements
Elements có thể được đánh dấu là persistent để không bị ẩn khi có collision:

```tsx
const persistent = useFloatingUIElement('id', content, {
  persistent: true, // Won't be hidden during collision resolution
});
```

## Best Practices

### 1. Unique IDs
Luôn sử dụng unique IDs cho mỗi floating element:

```tsx
// Good
const notification1 = useFloatingNotification('notification-1', content);
const notification2 = useFloatingNotification('notification-2', content);

// Bad - same ID will cause conflicts
const notification1 = useFloatingNotification('notification', content);
const notification2 = useFloatingNotification('notification', content);
```

### 2. Cleanup
Floating elements tự động cleanup khi component unmount, nhưng có thể manual cleanup:

```tsx
useEffect(() => {
  return () => {
    // Manual cleanup if needed
    hide();
  };
}, [hide]);
```

### 3. Performance
Sử dụng React.memo cho content nặng:

```tsx
const HeavyContent = React.memo(() => (
  <div>
    {/* Heavy content */}
  </div>
));

const element = useFloatingUIElement('id', <HeavyContent />);
```

### 4. Accessibility
Đảm bảo floating elements có proper ARIA attributes:

```tsx
const accessibleContent = (
  <div role="dialog" aria-labelledby="modal-title" aria-describedby="modal-desc">
    <h2 id="modal-title">Modal Title</h2>
    <p id="modal-desc">Modal description</p>
  </div>
);
```

## Styling

### CSS Classes
Floating UI system tự động thêm các CSS classes:

- `.floating-ui-element` - Base class cho tất cả elements
- `.floating-ui-{type}` - Type-specific classes
- `.floating-ui-priority-{priority}` - Priority-specific classes
- `.floating-ui-collision` - Applied khi có collision

### Custom Styling
```tsx
const styled = useFloatingUIElement('id', content, {
  className: 'my-custom-class',
  style: {
    backgroundColor: 'red',
    borderRadius: '8px',
  },
});
```

## Troubleshooting

### Common Issues

1. **Elements không hiển thị**: Kiểm tra `autoShow` option hoặc gọi `show()` manually
2. **Z-index conflicts**: Sử dụng priority system thay vì manual z-index
3. **Performance issues**: Sử dụng React.memo cho heavy content
4. **Mobile layout issues**: Kiểm tra responsive breakpoints và mobile-specific styles

### Debug Mode
```tsx
// Enable debug logging
const element = useFloatingUIElement('id', content, {
  onShow: () => console.log('Element shown'),
  onHide: () => console.log('Element hidden'),
  onCollision: (colliding) => console.log('Collision:', colliding),
});
```
