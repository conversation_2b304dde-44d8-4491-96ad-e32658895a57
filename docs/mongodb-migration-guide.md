# MongoDB Migration Guide

## Overview

This guide provides step-by-step instructions for migrating the Vocab application from PostgreSQL to MongoDB. The migration has been designed to minimize downtime and ensure data integrity.

## Prerequisites

### Environment Setup

1. **MongoDB Instance**: Ensure you have a MongoDB instance ready (local, cloud, or containerized)
2. **Environment Variables**: Update your `.env` file with MongoDB configuration
3. **Dependencies**: All required packages should be installed via `yarn install`

### Required Environment Variables

```bash
# MongoDB Configuration
MONGODB_URI="mongodb://localhost:27017/vocab"
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=2
MONGODB_MAX_IDLE_TIME_MS=30000
MONGODB_SERVER_SELECTION_TIMEOUT_MS=5000
MONGODB_SOCKET_TIMEOUT_MS=45000

# Feature Flags
FEATURE_MONGODB_ENABLED=true
FEATURE_DUAL_DATABASE=false
```

## Migration Phases

### Phase 1: Setup & Configuration ✅

- [x] Install MongoDB dependencies
- [x] Create MongoDB connection configuration
- [x] Define MongoDB schemas and types
- [x] Create base MongoDB repository

### Phase 2: Repository Layer ✅

- [x] Implement MongoDB repositories for all models
- [x] Create database abstraction layer
- [x] Update dependency injection

### Phase 3: Data Migration ✅

- [x] Create PostgreSQL export utilities
- [x] Create data transformation scripts
- [x] Create MongoDB import utilities

### Phase 4: Testing & Validation ✅

- [x] Unit tests for MongoDB repositories
- [x] Integration tests
- [x] Performance testing and optimization

### Phase 5: Production Migration 🚀

This phase involves the actual production migration with minimal downtime.

## Migration Scripts

### Available Commands

```bash
# Individual migration steps
yarn migration:export          # Export data from PostgreSQL
yarn migration:transform       # Transform data for MongoDB
yarn migration:import          # Import data to MongoDB

# Full migration (recommended)
yarn migration:full            # Complete migration process
yarn migration:full:drop       # Migration with existing data cleanup
yarn migration:full:cleanup    # Migration with temporary file cleanup

# Testing
yarn test:mongo               # Run MongoDB unit tests
yarn test:mongo:coverage      # Run tests with coverage
yarn benchmark:mongodb        # Performance benchmarking
```

## Step-by-Step Migration Process

### Step 1: Pre-Migration Preparation

1. **Backup Current Database**
   ```bash
   pg_dump vocab > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Run Tests**
   ```bash
   yarn test:mongo
   yarn test:mongo:coverage
   ```

3. **Performance Baseline**
   ```bash
   yarn benchmark:mongodb
   ```

### Step 2: Setup MongoDB

1. **Start MongoDB** (if using Docker)
   ```bash
   docker-compose up -d mongodb
   ```

2. **Verify Connection**
   ```bash
   # Test MongoDB connection
   mongosh $MONGODB_URI
   ```

### Step 3: Execute Migration

#### Option A: Full Automated Migration (Recommended)

```bash
# Complete migration with validation
yarn migration:full

# Or with cleanup of temporary files
yarn migration:full:cleanup
```

#### Option B: Step-by-Step Migration

```bash
# Step 1: Export from PostgreSQL
yarn migration:export

# Step 2: Transform data
yarn migration:transform

# Step 3: Import to MongoDB
yarn migration:import
```

### Step 4: Switch to MongoDB

1. **Update Environment Variables**
   ```bash
   FEATURE_MONGODB_ENABLED=true
   FEATURE_DUAL_DATABASE=false
   ```

2. **Restart Application**
   ```bash
   yarn build
   yarn start
   ```

3. **Verify Application Functionality**
   - Test user authentication
   - Test word creation and retrieval
   - Test collection management
   - Test all major features

### Step 5: Post-Migration Validation

1. **Data Integrity Check**
   ```bash
   # Compare record counts
   # PostgreSQL: SELECT COUNT(*) FROM users;
   # MongoDB: db.users.countDocuments()
   ```

2. **Performance Testing**
   ```bash
   yarn benchmark:mongodb
   ```

3. **Application Testing**
   - Run full test suite
   - Manual testing of critical paths
   - Load testing (if applicable)

## Rollback Plan

If issues are encountered during migration:

### Immediate Rollback

1. **Switch Back to PostgreSQL**
   ```bash
   FEATURE_MONGODB_ENABLED=false
   FEATURE_DUAL_DATABASE=false
   ```

2. **Restart Application**
   ```bash
   yarn build
   yarn start
   ```

### Data Recovery

1. **Restore from Backup** (if needed)
   ```bash
   psql vocab < backup_YYYYMMDD_HHMMSS.sql
   ```

## Monitoring and Maintenance

### MongoDB Monitoring

1. **Connection Status**
   ```javascript
   // Check in application
   const status = getDatabaseConnectionStatus();
   console.log(status);
   ```

2. **Performance Metrics**
   - Query execution times
   - Index usage
   - Memory usage
   - Connection pool status

### Index Optimization

MongoDB schemas include optimized indexes for:
- User lookups by provider and provider_id
- Word searches by term and language
- Collection queries by user_id
- Text search capabilities

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check MongoDB server status
   - Verify network connectivity
   - Adjust timeout settings in environment variables

2. **Memory Issues**
   - Monitor MongoDB memory usage
   - Adjust connection pool settings
   - Consider index optimization

3. **Performance Degradation**
   - Run benchmark tests
   - Check index usage
   - Optimize queries

### Debug Commands

```bash
# Check MongoDB status
mongosh --eval "db.adminCommand('serverStatus')"

# Check database size
mongosh --eval "db.stats()"

# Check collection indexes
mongosh --eval "db.users.getIndexes()"
```

## Data Structure Changes

### Key Differences from PostgreSQL

1. **Embedded Documents**: Related data is embedded within documents
   - Word definitions, explains, and examples are embedded
   - Paragraph multiple choice exercises are embedded

2. **Array Fields**: Collections use arrays for related IDs
   - `word_ids`, `paragraph_ids`, `keyword_ids` in collections

3. **ObjectId**: MongoDB uses ObjectId instead of integer IDs
   - Automatic conversion handled by repositories

### Schema Mapping

| PostgreSQL Table | MongoDB Collection | Notes |
|------------------|-------------------|-------|
| users | users | Direct mapping |
| words | words | Embedded definitions |
| definitions | - | Embedded in words |
| explains | - | Embedded in definitions |
| examples | - | Embedded in definitions |
| collections | collections | Array fields for IDs |
| paragraphs | paragraphs | Embedded exercises |
| multiple_choice_exercises | - | Embedded in paragraphs |
| keywords | keywords | Direct mapping |
| last_seen_words | last_seen_words | Direct mapping |
| feedbacks | feedbacks | Direct mapping |
| collection_stats | collection_stats | Direct mapping |

## Performance Considerations

### Optimizations Implemented

1. **Indexes**: Comprehensive indexing strategy
2. **Connection Pooling**: Optimized connection management
3. **Query Optimization**: Efficient query patterns
4. **Batch Operations**: Bulk insert/update operations

### Expected Performance

Based on benchmarking:
- User operations: ~1000 ops/sec
- Word searches: ~500 ops/sec
- Complex queries: ~200 ops/sec
- Aggregations: ~100 ops/sec

## Support and Maintenance

### Regular Maintenance Tasks

1. **Index Maintenance**
   ```bash
   # Rebuild indexes if needed
   mongosh --eval "db.words.reIndex()"
   ```

2. **Performance Monitoring**
   ```bash
   # Regular benchmarking
   yarn benchmark:mongodb
   ```

3. **Backup Strategy**
   ```bash
   # MongoDB backup
   mongodump --uri="$MONGODB_URI" --out=backup_$(date +%Y%m%d)
   ```

### Getting Help

- Check application logs for detailed error messages
- Use MongoDB profiler for query analysis
- Monitor connection pool metrics
- Review benchmark results for performance trends

## Conclusion

This migration guide provides a comprehensive approach to migrating from PostgreSQL to MongoDB with minimal risk and downtime. Follow the steps carefully and ensure proper testing at each stage.

For any issues or questions, refer to the troubleshooting section or check the application logs for detailed error information.
