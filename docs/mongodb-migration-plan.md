# Kế hoạch chuyển đổi Database từ PostgreSQL sang MongoDB

## Tổng quan

Dự án Vocab hiện tại đang sử dụng PostgreSQL với Prisma ORM. Kế hoạch này mô tả chi tiết quá trình chuyển đổi sang MongoDB để tận dụng tính linh hoạt của NoSQL database cho ứng dụng học từ vựng.

## Mục tiêu chuyển đổi

### Lý do chuyển đổi
- **Tính linh hoạt schema**: MongoDB cho phép thay đổi cấu trúc dữ liệu dễ dàng hơn
- **Hiệu suất với dữ liệu lớn**: Tối ưu cho việc lưu trữ và truy vấn dữ liệu từ vựng phức tạp
- **T<PERSON><PERSON> hợ<PERSON> AI tốt hơn**: Lưu trữ embeddings và dữ liệu AI-generated hiệu quả
- **Scalability**: <PERSON><PERSON> dàng mở rộng theo chiều ngang

### Lợi ích mong đợi
- Cải thiện hiệu suất truy vấn với dữ liệu từ vựng phức tạp
- Linh hoạt trong việc thêm tính năng AI mới
- Giảm độ phức tạp của schema migrations
- Tối ưu hóa cho việc lưu trữ dữ liệu đa ngôn ngữ

## Phân tích hiện trạng

### Database hiện tại
- **Provider**: PostgreSQL
- **ORM**: Prisma Client
- **Models chính**: 15 models với quan hệ phức tạp
- **Enums**: 5 enums (Language, Provider, PartsOfSpeech, Difficulty, Length)
- **Tính năng đặc biệt**: UUID, arrays, JSON fields

### Cấu trúc Models hiện tại
```
User (1) -> (N) Collection
User (1) -> (N) Keyword  
User (1) -> (N) LastSeenWord
User (1) -> (N) Feedback
User (1) -> (N) CollectionStats

Word (1) -> (N) Definition
Definition (1) -> (N) Explain
Definition (1) -> (N) Example

Paragraph (1) -> (N) MultipleChoiceExercise
```

## Chiến lược chuyển đổi

### 1. Embedded vs Referenced Documents

#### Embedded Documents (Nhúng)
- **Definition** trong **Word**: Tối ưu cho truy vấn từ vựng
- **Explain** và **Example** trong **Definition**: Giảm số lượng queries
- **MultipleChoiceExercise** trong **Paragraph**: Tăng hiệu suất loading

#### Referenced Documents (Tham chiếu)
- **User** collections: Giữ riêng biệt để quản lý authentication
- **Collection**: Tham chiếu đến User và Word IDs
- **CollectionStats**: Tham chiếu đến User và Collection

### 2. Schema Design MongoDB

#### Users Collection
```javascript
{
  _id: ObjectId,
  provider: "TELEGRAM" | "USERNAME_PASSWORD" | "GOOGLE",
  provider_id: String,
  username: String?,
  password_hash: String?,
  disabled: Boolean,
  created_at: Date,
  updated_at: Date,
  // Indexes
  indexes: [
    { "provider": 1, "provider_id": 1 }, // unique
    { "username": 1 } // unique, sparse
  ]
}
```

#### Words Collection
```javascript
{
  _id: ObjectId,
  term: String,
  language: "EN" | "VI",
  audio_url: String?,
  definitions: [
    {
      _id: ObjectId,
      pos: ["NOUN", "VERB", ...],
      ipa: String,
      images: [String],
      explains: [
        {
          _id: ObjectId,
          EN: String,
          VI: String
        }
      ],
      examples: [
        {
          _id: ObjectId,
          EN: String,
          VI: String
        }
      ]
    }
  ],
  created_at: Date,
  updated_at: Date,
  // Indexes
  indexes: [
    { "term": 1, "language": 1 }, // unique
    { "term": "text" }, // text search
    { "language": 1 }
  ]
}
```

#### Collections Collection
```javascript
{
  _id: ObjectId,
  name: String,
  target_language: "EN" | "VI",
  source_language: "EN" | "VI",
  user_id: ObjectId, // reference to Users
  word_ids: [ObjectId], // references to Words
  paragraph_ids: [ObjectId], // references to Paragraphs
  keyword_ids: [ObjectId], // references to Keywords
  enable_learn_word_notification: Boolean,
  created_at: Date,
  updated_at: Date,
  // Indexes
  indexes: [
    { "user_id": 1 },
    { "user_id": 1, "name": 1 }
  ]
}
```

## Kế hoạch thực hiện

### Phase 1: Chuẩn bị và Setup (1-2 tuần)

#### 1.1 Cài đặt MongoDB và Dependencies
```bash
# Cài đặt MongoDB dependencies
yarn add mongodb mongoose
yarn add -D @types/mongodb

# Cài đặt MongoDB Compass (GUI tool)
# Setup MongoDB Atlas hoặc local MongoDB instance
```

#### 1.2 Cấu hình MongoDB Connection
- Tạo file `src/config/mongodb.ts`
- Cập nhật environment variables
- Setup connection pooling và error handling

#### 1.3 Tạo MongoDB Schema Definitions
- Tạo Mongoose schemas trong `src/backend/schemas/`
- Định nghĩa indexes và validation rules
- Setup TypeScript types cho MongoDB documents

### Phase 2: Tạo Data Access Layer mới (2-3 tuần)

#### 2.1 Tạo MongoDB Repositories
- Implement `BaseMongoRepository<T>`
- Tạo specific repositories cho từng collection
- Maintain interface compatibility với Prisma repositories

#### 2.2 Tạo Migration Utilities
- Data export tools từ PostgreSQL
- Data transformation scripts
- Data import tools vào MongoDB
- Validation scripts để đảm bảo data integrity

#### 2.3 Dual Database Support
- Implement feature flag để switch giữa PostgreSQL và MongoDB
- Tạo abstraction layer cho database operations
- Setup parallel testing với cả hai databases

### Phase 3: Data Migration (1-2 tuần)

#### 3.1 Export Data từ PostgreSQL
```bash
# Export script sẽ tạo JSON files cho từng table
node scripts/export-postgresql-data.js
```

#### 3.2 Transform Data Structure
- Convert relational data sang document structure
- Merge related tables thành embedded documents
- Handle foreign key relationships

#### 3.3 Import vào MongoDB
```bash
# Import script với validation
node scripts/import-mongodb-data.js
```

### Phase 4: Testing và Validation (2-3 tuần)

#### 4.1 Unit Testing
- Test tất cả MongoDB repositories
- Test data transformation logic
- Test query performance

#### 4.2 Integration Testing
- Test API endpoints với MongoDB
- Test authentication flows
- Test complex queries và aggregations

#### 4.3 Performance Testing
- Benchmark query performance
- Test với large datasets
- Optimize indexes và queries

### Phase 5: Production Migration (1 tuần)

#### 5.1 Pre-migration Checklist
- [ ] Backup PostgreSQL database
- [ ] Setup MongoDB production instance
- [ ] Test migration scripts với production data copy
- [ ] Prepare rollback plan

#### 5.2 Migration Execution
- [ ] Maintenance mode activation
- [ ] Run migration scripts
- [ ] Validate data integrity
- [ ] Switch application to MongoDB
- [ ] Monitor system performance

#### 5.3 Post-migration Tasks
- [ ] Remove PostgreSQL dependencies
- [ ] Update documentation
- [ ] Clean up old migration code
- [ ] Performance monitoring setup

## Rủi ro và Mitigation

### Rủi ro cao
1. **Data Loss**: Implement comprehensive backup và validation
2. **Performance Degradation**: Extensive testing và optimization
3. **Downtime**: Minimize với careful planning và rehearsal

### Rủi ro trung bình
1. **Query Complexity**: MongoDB aggregation pipeline learning curve
2. **Transaction Support**: MongoDB transactions khác với PostgreSQL
3. **Tool Compatibility**: Some tools có thể không support MongoDB

### Mitigation Strategies
- Comprehensive testing environment
- Gradual rollout với feature flags
- Detailed rollback procedures
- Performance monitoring và alerting

## Timeline tổng thể

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1 | 1-2 tuần | MongoDB setup, connection config |
| Phase 2 | 2-3 tuần | New repositories, migration tools |
| Phase 3 | 1-2 tuần | Data migration completed |
| Phase 4 | 2-3 tuần | Testing và validation |
| Phase 5 | 1 tuần | Production migration |
| **Total** | **7-11 tuần** | **Complete MongoDB migration** |

## Tài nguyên cần thiết

### Technical Resources
- MongoDB Atlas cluster hoặc self-hosted MongoDB
- Development và staging environments
- Backup storage cho PostgreSQL data

### Human Resources
- 1 Senior Developer (MongoDB experience)
- 1 DevOps Engineer (database migration)
- 1 QA Engineer (testing và validation)

### Tools và Services
- MongoDB Compass
- MongoDB Atlas (recommended)
- Data migration tools
- Performance monitoring tools

## Kết luận

Migration từ PostgreSQL sang MongoDB là một dự án phức tạp nhưng sẽ mang lại nhiều lợi ích lâu dài cho ứng dụng Vocab. Với kế hoạch chi tiết và thực hiện cẩn thận, chúng ta có thể đảm bảo migration thành công với minimal downtime và data integrity.

Bước tiếp theo: Review kế hoạch với team và bắt đầu Phase 1 setup.
