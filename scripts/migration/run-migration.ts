#!/usr/bin/env tsx

import { PostgreSQLExporter, ExportConfig } from './export-postgresql';
import { DataTransformer, TransformConfig } from './transform-data';
import { MongoDBImporter, ImportConfig } from './import-mongodb';
import path from 'path';
import fs from 'fs/promises';

// Migration configuration
interface MigrationConfig {
	dataDir: string;
	exportConfig: ExportConfig;
	transformConfig: TransformConfig;
	importConfig: ImportConfig;
	cleanupAfter: boolean;
	validateIntegrity: boolean;
}

// Migration statistics
interface MigrationStats {
	startTime: Date;
	endTime?: Date;
	duration?: number;
	exportStats: any;
	transformStats: any;
	importStats: any;
	success: boolean;
	errors: string[];
}

class MigrationRunner {
	private config: MigrationConfig;
	private stats: MigrationStats;

	constructor(config: MigrationConfig) {
		this.config = config;
		this.stats = {
			startTime: new Date(),
			exportStats: null,
			transformStats: null,
			importStats: null,
			success: false,
			errors: [],
		};
	}

	async runFullMigration(): Promise<void> {
		console.log('🚀 Starting full PostgreSQL to MongoDB migration...');
		console.log(`📁 Data directory: ${this.config.dataDir}`);
		
		try {
			// Ensure data directory exists
			await fs.mkdir(this.config.dataDir, { recursive: true });
			
			// Step 1: Export from PostgreSQL
			console.log('\n' + '='.repeat(60));
			console.log('📤 STEP 1: EXPORTING FROM POSTGRESQL');
			console.log('='.repeat(60));
			await this.exportFromPostgreSQL();
			
			// Step 2: Transform data
			console.log('\n' + '='.repeat(60));
			console.log('🔄 STEP 2: TRANSFORMING DATA');
			console.log('='.repeat(60));
			await this.transformData();
			
			// Step 3: Import to MongoDB
			console.log('\n' + '='.repeat(60));
			console.log('📥 STEP 3: IMPORTING TO MONGODB');
			console.log('='.repeat(60));
			await this.importToMongoDB();
			
			// Step 4: Validate integrity (optional)
			if (this.config.validateIntegrity) {
				console.log('\n' + '='.repeat(60));
				console.log('✅ STEP 4: VALIDATING DATA INTEGRITY');
				console.log('='.repeat(60));
				await this.validateDataIntegrity();
			}
			
			// Step 5: Cleanup (optional)
			if (this.config.cleanupAfter) {
				console.log('\n' + '='.repeat(60));
				console.log('🧹 STEP 5: CLEANING UP TEMPORARY FILES');
				console.log('='.repeat(60));
				await this.cleanup();
			}
			
			this.stats.success = true;
			this.stats.endTime = new Date();
			this.stats.duration = this.stats.endTime.getTime() - this.stats.startTime.getTime();
			
			console.log('\n' + '='.repeat(60));
			console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
			console.log('='.repeat(60));
			this.printFinalSummary();
			
		} catch (error) {
			this.stats.success = false;
			this.stats.errors.push(String(error));
			this.stats.endTime = new Date();
			this.stats.duration = this.stats.endTime.getTime() - this.stats.startTime.getTime();
			
			console.error('\n' + '='.repeat(60));
			console.error('💥 MIGRATION FAILED!');
			console.error('='.repeat(60));
			console.error('Error:', error);
			this.printFinalSummary();
			throw error;
		}
	}

	private async exportFromPostgreSQL(): Promise<void> {
		try {
			const exporter = new PostgreSQLExporter(this.config.exportConfig);
			await exporter.initialize();
			await exporter.exportAllData();
			await exporter.cleanup();
			console.log('✅ PostgreSQL export completed');
		} catch (error) {
			console.error('❌ PostgreSQL export failed:', error);
			throw error;
		}
	}

	private async transformData(): Promise<void> {
		try {
			const transformer = new DataTransformer(this.config.transformConfig);
			await transformer.initialize();
			await transformer.transformAllData();
			console.log('✅ Data transformation completed');
		} catch (error) {
			console.error('❌ Data transformation failed:', error);
			throw error;
		}
	}

	private async importToMongoDB(): Promise<void> {
		try {
			const importer = new MongoDBImporter(this.config.importConfig);
			await importer.initialize();
			await importer.importAllData();
			await importer.cleanup();
			console.log('✅ MongoDB import completed');
		} catch (error) {
			console.error('❌ MongoDB import failed:', error);
			throw error;
		}
	}

	private async validateDataIntegrity(): Promise<void> {
		console.log('🔍 Validating data integrity...');
		
		try {
			// Basic validation - check if files exist and have data
			const exportDir = this.config.exportConfig.outputDir;
			const importDir = this.config.transformConfig.outputDir;
			
			const expectedFiles = [
				'users.json',
				'words.json',
				'collections.json',
				'paragraphs.json',
				'keywords.json',
				'last_seen_words.json',
				'feedbacks.json',
				'collection_stats.json',
			];
			
			for (const filename of expectedFiles) {
				// Check export file
				const exportFile = path.join(exportDir, filename);
				const exportData = JSON.parse(await fs.readFile(exportFile, 'utf8'));
				
				// Check transform file
				const importFile = path.join(importDir, filename);
				const importData = JSON.parse(await fs.readFile(importFile, 'utf8'));
				
				console.log(`   ${filename}: ${exportData.length} → ${importData.length} records`);
				
				if (exportData.length !== importData.length) {
					throw new Error(`Record count mismatch in ${filename}: ${exportData.length} vs ${importData.length}`);
				}
			}
			
			console.log('✅ Data integrity validation passed');
		} catch (error) {
			console.error('❌ Data integrity validation failed:', error);
			throw error;
		}
	}

	private async cleanup(): Promise<void> {
		console.log('🧹 Cleaning up temporary files...');
		
		try {
			// Remove export directory
			await fs.rm(this.config.exportConfig.outputDir, { recursive: true, force: true });
			console.log(`   ✅ Removed ${this.config.exportConfig.outputDir}`);
			
			// Keep transform directory for reference
			console.log(`   📁 Keeping ${this.config.transformConfig.outputDir} for reference`);
			
			console.log('✅ Cleanup completed');
		} catch (error) {
			console.error('❌ Cleanup failed:', error);
			// Don't throw error for cleanup failure
		}
	}

	private printFinalSummary(): void {
		const duration = this.stats.duration ? Math.round(this.stats.duration / 1000) : 0;
		
		console.log('\n📊 Migration Summary:');
		console.log(`   Status: ${this.stats.success ? '✅ SUCCESS' : '❌ FAILED'}`);
		console.log(`   Duration: ${duration} seconds`);
		console.log(`   Start time: ${this.stats.startTime.toISOString()}`);
		console.log(`   End time: ${this.stats.endTime?.toISOString() || 'N/A'}`);
		
		if (this.stats.errors.length > 0) {
			console.log(`   Errors: ${this.stats.errors.length}`);
			for (const error of this.stats.errors) {
				console.log(`     - ${error}`);
			}
		}
	}
}

// Default configuration
function getDefaultConfig(): MigrationConfig {
	const dataDir = path.join(process.cwd(), 'data');
	
	return {
		dataDir,
		exportConfig: {
			outputDir: path.join(dataDir, 'postgresql-export'),
			batchSize: 1000,
			includeRelations: true,
			validateData: true,
		},
		transformConfig: {
			inputDir: path.join(dataDir, 'postgresql-export'),
			outputDir: path.join(dataDir, 'mongodb-import'),
			validateOutput: true,
			preserveIds: true,
		},
		importConfig: {
			inputDir: path.join(dataDir, 'mongodb-import'),
			batchSize: 100,
			validateData: true,
			dropExisting: false,
			createIndexes: true,
		},
		cleanupAfter: false,
		validateIntegrity: true,
	};
}

// CLI argument parsing
function parseArguments(): Partial<MigrationConfig> {
	const args = process.argv.slice(2);
	const config: Partial<MigrationConfig> = {};
	
	for (let i = 0; i < args.length; i++) {
		const arg = args[i];
		
		switch (arg) {
			case '--cleanup':
				config.cleanupAfter = true;
				break;
			case '--no-validation':
				config.validateIntegrity = false;
				break;
			case '--drop-existing':
				if (config.importConfig) {
					config.importConfig.dropExisting = true;
				} else {
					config.importConfig = { dropExisting: true } as any;
				}
				break;
			case '--data-dir':
				if (i + 1 < args.length) {
					config.dataDir = args[i + 1];
					i++;
				}
				break;
		}
	}
	
	return config;
}

// Main execution
async function main() {
	console.log('🔄 PostgreSQL to MongoDB Migration Tool');
	console.log('=====================================\n');
	
	// Parse CLI arguments
	const cliConfig = parseArguments();
	const defaultConfig = getDefaultConfig();
	const config: MigrationConfig = { ...defaultConfig, ...cliConfig };
	
	// Update nested configs if dataDir was changed
	if (cliConfig.dataDir) {
		config.exportConfig.outputDir = path.join(config.dataDir, 'postgresql-export');
		config.transformConfig.inputDir = path.join(config.dataDir, 'postgresql-export');
		config.transformConfig.outputDir = path.join(config.dataDir, 'mongodb-import');
		config.importConfig.inputDir = path.join(config.dataDir, 'mongodb-import');
	}
	
	console.log('Configuration:');
	console.log(`  Data directory: ${config.dataDir}`);
	console.log(`  Cleanup after: ${config.cleanupAfter}`);
	console.log(`  Validate integrity: ${config.validateIntegrity}`);
	console.log(`  Drop existing: ${config.importConfig.dropExisting}`);
	
	const runner = new MigrationRunner(config);
	
	try {
		await runner.runFullMigration();
		process.exit(0);
	} catch (error) {
		console.error('\n💥 Migration failed:', error);
		process.exit(1);
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { MigrationRunner, MigrationConfig, MigrationStats };
