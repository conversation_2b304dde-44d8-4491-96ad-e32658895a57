#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import fs from 'fs/promises';
import path from 'path';

// Export configuration
interface ExportConfig {
	outputDir: string;
	batchSize: number;
	includeRelations: boolean;
	validateData: boolean;
}

// Export statistics
interface ExportStats {
	totalRecords: number;
	exportedRecords: number;
	errors: string[];
	startTime: Date;
	endTime?: Date;
	duration?: number;
}

class PostgreSQLExporter {
	private prisma: PrismaClient;
	private config: ExportConfig;
	private stats: Map<string, ExportStats> = new Map();

	constructor(config: ExportConfig) {
		this.prisma = new PrismaClient();
		this.config = config;
	}

	async initialize(): Promise<void> {
		try {
			await this.prisma.$connect();
			console.log('✅ Connected to PostgreSQL');

			// Ensure output directory exists
			await fs.mkdir(this.config.outputDir, { recursive: true });
			console.log(`📁 Output directory: ${this.config.outputDir}`);
		} catch (error) {
			console.error('❌ Failed to initialize exporter:', error);
			throw error;
		}
	}

	async exportAllData(): Promise<void> {
		console.log('🚀 Starting PostgreSQL data export...');

		try {
			// Export in dependency order (referenced tables first)
			await this.exportUsers();
			await this.exportWords();
			await this.exportDefinitions();
			await this.exportExplains();
			await this.exportExamples();
			await this.exportCollections();
			await this.exportKeywords();
			await this.exportParagraphs();
			await this.exportMultipleChoiceExercises();
			await this.exportLastSeenWords();
			await this.exportFeedbacks();
			await this.exportCollectionStats();

			console.log('\n📊 Export Summary:');
			this.printSummary();
		} catch (error) {
			console.error('❌ Export failed:', error);
			throw error;
		}
	}

	private async exportUsers(): Promise<void> {
		const tableName = 'users';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.user.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const users = await this.prisma.user.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = users.map((user) => ({
				id: user.id,
				provider: user.provider,
				provider_id: user.provider_id,
				username: user.username,
				password_hash: user.password_hash,
				disabled: user.disabled,
				created_at: user.created_at.toISOString(),
				updated_at: user.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportWords(): Promise<void> {
		const tableName = 'words';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.word.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const words = await this.prisma.word.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = words.map((word) => ({
				id: word.id,
				term: word.term,
				language: word.language,
				audio_url: word.audio_url,
				created_at: word.created_at.toISOString(),
				updated_at: word.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportDefinitions(): Promise<void> {
		const tableName = 'definitions';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.definition.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const definitions = await this.prisma.definition.findMany({
				orderBy: { word_id: 'asc' },
			});

			const exportData = definitions.map((definition) => ({
				id: definition.id,
				word_id: definition.word_id,
				pos: definition.pos,
				ipa: definition.ipa,
				images: definition.images,
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportExplains(): Promise<void> {
		const tableName = 'explains';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.explain.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const explains = await this.prisma.explain.findMany({
				orderBy: { definition_id: 'asc' },
			});

			const exportData = explains.map((explain) => ({
				id: explain.id,
				definition_id: explain.definition_id,
				EN: explain.EN,
				VI: explain.VI,
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportExamples(): Promise<void> {
		const tableName = 'examples';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.example.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const examples = await this.prisma.example.findMany({
				orderBy: { definition_id: 'asc' },
			});

			const exportData = examples.map((example) => ({
				id: example.id,
				definition_id: example.definition_id,
				EN: example.EN,
				VI: example.VI,
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportCollections(): Promise<void> {
		const tableName = 'collections';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.collection.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const collections = await this.prisma.collection.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = collections.map((collection) => ({
				id: collection.id,
				name: collection.name,
				target_language: collection.target_language,
				source_language: collection.source_language,
				user_id: collection.user_id,
				word_ids: collection.word_ids,
				paragraph_ids: collection.paragraph_ids,
				keyword_ids: collection.keyword_ids,
				enable_learn_word_notification: collection.enable_learn_word_notification,
				created_at: collection.created_at.toISOString(),
				updated_at: collection.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportKeywords(): Promise<void> {
		const tableName = 'keywords';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.keyword.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const keywords = await this.prisma.keyword.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = keywords.map((keyword) => ({
				id: keyword.id,
				content: keyword.content,
				user_id: keyword.user_id,
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportParagraphs(): Promise<void> {
		const tableName = 'paragraphs';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.paragraph.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const paragraphs = await this.prisma.paragraph.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = paragraphs.map((paragraph) => ({
				id: paragraph.id,
				title: paragraph.title,
				content: paragraph.content,
				language: paragraph.language,
				difficulty: paragraph.difficulty,
				length: paragraph.length,
				created_at: paragraph.created_at.toISOString(),
				updated_at: paragraph.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportMultipleChoiceExercises(): Promise<void> {
		const tableName = 'multiple_choice_exercises';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.multipleChoiceExercise.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const exercises = await this.prisma.multipleChoiceExercise.findMany({
				orderBy: { paragraph_id: 'asc' },
			});

			const exportData = exercises.map((exercise) => ({
				id: exercise.id,
				question: exercise.question,
				options: exercise.options,
				answer: exercise.answer,
				explanation: exercise.explanation,
				paragraph_id: exercise.paragraph_id,
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportLastSeenWords(): Promise<void> {
		const tableName = 'last_seen_words';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.lastSeenWord.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const lastSeenWords = await this.prisma.lastSeenWord.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = lastSeenWords.map((lsw) => ({
				id: lsw.id,
				user_id: lsw.user_id,
				word_id: lsw.word_id,
				last_seen_at: lsw.last_seen_at.toISOString(),
				review_count: lsw.review_count,
				created_at: lsw.created_at.toISOString(),
				updated_at: lsw.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportFeedbacks(): Promise<void> {
		const tableName = 'feedbacks';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.feedback.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const feedbacks = await this.prisma.feedback.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = feedbacks.map((feedback) => ({
				id: feedback.id,
				message: feedback.message,
				user_id: feedback.user_id,
				status: feedback.status,
				created_at: feedback.created_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async exportCollectionStats(): Promise<void> {
		const tableName = 'collection_stats';
		console.log(`\n📤 Exporting ${tableName}...`);

		const stats: ExportStats = {
			totalRecords: 0,
			exportedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(tableName, stats);

		try {
			const total = await this.prisma.collectionStats.count();
			stats.totalRecords = total;
			console.log(`   Total records: ${total}`);

			const collectionStats = await this.prisma.collectionStats.findMany({
				orderBy: { created_at: 'asc' },
			});

			const exportData = collectionStats.map((stat) => ({
				id: stat.id,
				collection_id: stat.collection_id,
				user_id: stat.user_id,
				date: stat.date.toISOString(),
				words_reviewed_count: stat.words_reviewed_count,
				qa_practice_submissions: stat.qa_practice_submissions,
				paragraph_practice_submissions: stat.paragraph_practice_submissions,
				created_at: stat.created_at.toISOString(),
				updated_at: stat.updated_at.toISOString(),
			}));

			await this.writeToFile(tableName, exportData);
			stats.exportedRecords = exportData.length;
			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();

			console.log(`   ✅ Exported ${stats.exportedRecords} records`);
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error exporting ${tableName}:`, error);
			throw error;
		}
	}

	private async writeToFile(tableName: string, data: any[]): Promise<void> {
		const filePath = path.join(this.config.outputDir, `${tableName}.json`);
		await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
	}

	private printSummary(): void {
		let totalRecords = 0;
		let totalExported = 0;
		let totalErrors = 0;

		for (const [tableName, stats] of this.stats.entries()) {
			totalRecords += stats.totalRecords;
			totalExported += stats.exportedRecords;
			totalErrors += stats.errors.length;

			console.log(
				`   ${tableName}: ${stats.exportedRecords}/${stats.totalRecords} (${stats.duration}ms)`
			);
		}

		console.log(`\n📈 Total: ${totalExported}/${totalRecords} records exported`);
		if (totalErrors > 0) {
			console.log(`⚠️  Total errors: ${totalErrors}`);
		}
	}

	async cleanup(): Promise<void> {
		await this.prisma.$disconnect();
		console.log('🔌 Disconnected from PostgreSQL');
	}
}

// Main execution
async function main() {
	const config: ExportConfig = {
		outputDir: path.join(process.cwd(), 'data', 'postgresql-export'),
		batchSize: 1000,
		includeRelations: true,
		validateData: true,
	};

	const exporter = new PostgreSQLExporter(config);

	try {
		await exporter.initialize();
		await exporter.exportAllData();
		console.log('\n🎉 Export completed successfully!');
	} catch (error) {
		console.error('\n💥 Export failed:', error);
		process.exit(1);
	} finally {
		await exporter.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { PostgreSQLExporter, ExportConfig, ExportStats };
