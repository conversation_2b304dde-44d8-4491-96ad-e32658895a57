#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';
import { connectToMongoDB, disconnectFromMongoDB, checkMongoDBHealth } from '../../src/config/mongodb';
import { UserModel, WordModel, CollectionModel } from '../../src/backend/schemas';
import fs from 'fs/promises';
import path from 'path';

// Readiness check configuration
interface ReadinessConfig {
	checkPostgreSQL: boolean;
	checkMongoDB: boolean;
	checkEnvironment: boolean;
	checkDependencies: boolean;
	checkDataIntegrity: boolean;
	checkPerformance: boolean;
}

// Check results
interface CheckResult {
	name: string;
	status: 'pass' | 'fail' | 'warning';
	message: string;
	details?: any;
}

class MigrationReadinessChecker {
	private config: ReadinessConfig;
	private results: CheckResult[] = [];
	private prisma: PrismaClient | null = null;

	constructor(config: ReadinessConfig) {
		this.config = config;
	}

	async runAllChecks(): Promise<boolean> {
		console.log('🔍 Running migration readiness checks...');
		
		try {
			if (this.config.checkEnvironment) {
				await this.checkEnvironmentVariables();
			}
			
			if (this.config.checkDependencies) {
				await this.checkDependencies();
			}
			
			if (this.config.checkPostgreSQL) {
				await this.checkPostgreSQLConnection();
				await this.checkPostgreSQLData();
			}
			
			if (this.config.checkMongoDB) {
				await this.checkMongoDBConnection();
				await this.checkMongoDBSetup();
			}
			
			if (this.config.checkDataIntegrity) {
				await this.checkDataIntegrity();
			}
			
			if (this.config.checkPerformance) {
				await this.checkPerformanceReadiness();
			}
			
			this.printResults();
			return this.isReadyForMigration();
		} catch (error) {
			console.error('❌ Readiness check failed:', error);
			return false;
		}
	}

	private async checkEnvironmentVariables(): Promise<void> {
		console.log('\n🌍 Checking environment variables...');
		
		const requiredVars = [
			'MONGODB_URI',
			'DATABASE_URL',
			'JWT_SECRET',
		];
		
		const optionalVars = [
			'MONGODB_MAX_POOL_SIZE',
			'MONGODB_MIN_POOL_SIZE',
			'FEATURE_MONGODB_ENABLED',
			'FEATURE_DUAL_DATABASE',
		];
		
		// Check required variables
		for (const varName of requiredVars) {
			if (!process.env[varName]) {
				this.addResult({
					name: `Environment Variable: ${varName}`,
					status: 'fail',
					message: `Required environment variable ${varName} is not set`,
				});
			} else {
				this.addResult({
					name: `Environment Variable: ${varName}`,
					status: 'pass',
					message: `${varName} is configured`,
				});
			}
		}
		
		// Check optional variables
		for (const varName of optionalVars) {
			if (!process.env[varName]) {
				this.addResult({
					name: `Environment Variable: ${varName}`,
					status: 'warning',
					message: `Optional environment variable ${varName} is not set (using defaults)`,
				});
			} else {
				this.addResult({
					name: `Environment Variable: ${varName}`,
					status: 'pass',
					message: `${varName} is configured`,
				});
			}
		}
	}

	private async checkDependencies(): Promise<void> {
		console.log('\n📦 Checking dependencies...');
		
		try {
			const packageJsonPath = path.join(process.cwd(), 'package.json');
			const packageJson = JSON.parse(await fs.readFile(packageJsonPath, 'utf8'));
			
			const requiredDeps = ['mongodb', 'mongoose'];
			const requiredDevDeps = ['mongodb-memory-server'];
			
			// Check production dependencies
			for (const dep of requiredDeps) {
				if (packageJson.dependencies?.[dep]) {
					this.addResult({
						name: `Dependency: ${dep}`,
						status: 'pass',
						message: `${dep} is installed (${packageJson.dependencies[dep]})`,
					});
				} else {
					this.addResult({
						name: `Dependency: ${dep}`,
						status: 'fail',
						message: `Required dependency ${dep} is not installed`,
					});
				}
			}
			
			// Check dev dependencies
			for (const dep of requiredDevDeps) {
				if (packageJson.devDependencies?.[dep]) {
					this.addResult({
						name: `Dev Dependency: ${dep}`,
						status: 'pass',
						message: `${dep} is installed (${packageJson.devDependencies[dep]})`,
					});
				} else {
					this.addResult({
						name: `Dev Dependency: ${dep}`,
						status: 'warning',
						message: `Dev dependency ${dep} is not installed (needed for testing)`,
					});
				}
			}
		} catch (error) {
			this.addResult({
				name: 'Dependencies Check',
				status: 'fail',
				message: `Failed to check dependencies: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkPostgreSQLConnection(): Promise<void> {
		console.log('\n🐘 Checking PostgreSQL connection...');
		
		try {
			this.prisma = new PrismaClient();
			await this.prisma.$connect();
			
			this.addResult({
				name: 'PostgreSQL Connection',
				status: 'pass',
				message: 'Successfully connected to PostgreSQL',
			});
		} catch (error) {
			this.addResult({
				name: 'PostgreSQL Connection',
				status: 'fail',
				message: `Failed to connect to PostgreSQL: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkPostgreSQLData(): Promise<void> {
		console.log('\n📊 Checking PostgreSQL data...');
		
		if (!this.prisma) {
			this.addResult({
				name: 'PostgreSQL Data Check',
				status: 'fail',
				message: 'Cannot check data - PostgreSQL connection failed',
			});
			return;
		}
		
		try {
			const userCount = await this.prisma.user.count();
			const wordCount = await this.prisma.word.count();
			const collectionCount = await this.prisma.collection.count();
			
			this.addResult({
				name: 'PostgreSQL Data',
				status: 'pass',
				message: `Data found: ${userCount} users, ${wordCount} words, ${collectionCount} collections`,
				details: { userCount, wordCount, collectionCount },
			});
			
			if (userCount === 0) {
				this.addResult({
					name: 'PostgreSQL Data Volume',
					status: 'warning',
					message: 'No users found - migration may not be necessary',
				});
			}
		} catch (error) {
			this.addResult({
				name: 'PostgreSQL Data Check',
				status: 'fail',
				message: `Failed to check PostgreSQL data: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkMongoDBConnection(): Promise<void> {
		console.log('\n🍃 Checking MongoDB connection...');
		
		try {
			await connectToMongoDB();
			const health = await checkMongoDBHealth();
			
			if (health === 'healthy') {
				this.addResult({
					name: 'MongoDB Connection',
					status: 'pass',
					message: 'Successfully connected to MongoDB',
				});
			} else {
				this.addResult({
					name: 'MongoDB Connection',
					status: 'fail',
					message: `MongoDB health check failed: ${health}`,
				});
			}
		} catch (error) {
			this.addResult({
				name: 'MongoDB Connection',
				status: 'fail',
				message: `Failed to connect to MongoDB: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkMongoDBSetup(): Promise<void> {
		console.log('\n⚙️  Checking MongoDB setup...');
		
		try {
			// Check if collections exist
			const collections = await Promise.all([
				UserModel.collection.countDocuments(),
				WordModel.collection.countDocuments(),
				CollectionModel.collection.countDocuments(),
			]);
			
			const [userCount, wordCount, collectionCount] = collections;
			
			if (userCount > 0 || wordCount > 0 || collectionCount > 0) {
				this.addResult({
					name: 'MongoDB Data',
					status: 'warning',
					message: `Existing data found: ${userCount} users, ${wordCount} words, ${collectionCount} collections`,
					details: { userCount, wordCount, collectionCount },
				});
			} else {
				this.addResult({
					name: 'MongoDB Data',
					status: 'pass',
					message: 'MongoDB is empty and ready for migration',
				});
			}
			
			// Check indexes
			const userIndexes = await UserModel.collection.getIndexes();
			const wordIndexes = await WordModel.collection.getIndexes();
			
			this.addResult({
				name: 'MongoDB Indexes',
				status: 'pass',
				message: `Indexes ready: ${Object.keys(userIndexes).length} user indexes, ${Object.keys(wordIndexes).length} word indexes`,
			});
		} catch (error) {
			this.addResult({
				name: 'MongoDB Setup Check',
				status: 'fail',
				message: `Failed to check MongoDB setup: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkDataIntegrity(): Promise<void> {
		console.log('\n🔍 Checking data integrity...');
		
		if (!this.prisma) {
			this.addResult({
				name: 'Data Integrity Check',
				status: 'warning',
				message: 'Cannot check data integrity - PostgreSQL connection failed',
			});
			return;
		}
		
		try {
			// Check for orphaned records
			const orphanedDefinitions = await this.prisma.definition.count({
				where: {
					word: null,
				},
			});
			
			const orphanedExplains = await this.prisma.explain.count({
				where: {
					definition: null,
				},
			});
			
			if (orphanedDefinitions > 0 || orphanedExplains > 0) {
				this.addResult({
					name: 'Data Integrity',
					status: 'warning',
					message: `Found orphaned records: ${orphanedDefinitions} definitions, ${orphanedExplains} explains`,
				});
			} else {
				this.addResult({
					name: 'Data Integrity',
					status: 'pass',
					message: 'No orphaned records found',
				});
			}
		} catch (error) {
			this.addResult({
				name: 'Data Integrity Check',
				status: 'fail',
				message: `Failed to check data integrity: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private async checkPerformanceReadiness(): Promise<void> {
		console.log('\n⚡ Checking performance readiness...');
		
		try {
			// Check available memory
			const memUsage = process.memoryUsage();
			const freeMemMB = (memUsage.heapTotal - memUsage.heapUsed) / 1024 / 1024;
			
			if (freeMemMB < 100) {
				this.addResult({
					name: 'Memory Availability',
					status: 'warning',
					message: `Low available memory: ${freeMemMB.toFixed(2)}MB`,
				});
			} else {
				this.addResult({
					name: 'Memory Availability',
					status: 'pass',
					message: `Sufficient memory available: ${freeMemMB.toFixed(2)}MB`,
				});
			}
			
			// Check disk space (simplified check)
			this.addResult({
				name: 'Performance Readiness',
				status: 'pass',
				message: 'System appears ready for migration',
			});
		} catch (error) {
			this.addResult({
				name: 'Performance Readiness Check',
				status: 'warning',
				message: `Could not fully assess performance readiness: ${error instanceof Error ? error.message : 'Unknown error'}`,
			});
		}
	}

	private addResult(result: CheckResult): void {
		this.results.push(result);
		const icon = result.status === 'pass' ? '✅' : result.status === 'warning' ? '⚠️' : '❌';
		console.log(`   ${icon} ${result.name}: ${result.message}`);
	}

	private printResults(): void {
		console.log('\n' + '='.repeat(80));
		console.log('📋 MIGRATION READINESS SUMMARY');
		console.log('='.repeat(80));
		
		const passed = this.results.filter(r => r.status === 'pass').length;
		const warnings = this.results.filter(r => r.status === 'warning').length;
		const failed = this.results.filter(r => r.status === 'fail').length;
		
		console.log(`✅ Passed: ${passed}`);
		console.log(`⚠️  Warnings: ${warnings}`);
		console.log(`❌ Failed: ${failed}`);
		console.log(`📊 Total: ${this.results.length}`);
		
		if (failed > 0) {
			console.log('\n❌ FAILED CHECKS:');
			this.results.filter(r => r.status === 'fail').forEach(r => {
				console.log(`   - ${r.name}: ${r.message}`);
			});
		}
		
		if (warnings > 0) {
			console.log('\n⚠️  WARNINGS:');
			this.results.filter(r => r.status === 'warning').forEach(r => {
				console.log(`   - ${r.name}: ${r.message}`);
			});
		}
	}

	private isReadyForMigration(): boolean {
		const failed = this.results.filter(r => r.status === 'fail').length;
		const ready = failed === 0;
		
		console.log('\n' + '='.repeat(80));
		if (ready) {
			console.log('🎉 READY FOR MIGRATION!');
			console.log('You can proceed with the migration process.');
		} else {
			console.log('🚫 NOT READY FOR MIGRATION');
			console.log('Please fix the failed checks before proceeding.');
		}
		console.log('='.repeat(80));
		
		return ready;
	}

	async cleanup(): Promise<void> {
		if (this.prisma) {
			await this.prisma.$disconnect();
		}
		await disconnectFromMongoDB();
	}
}

// Main execution
async function main() {
	const config: ReadinessConfig = {
		checkPostgreSQL: true,
		checkMongoDB: true,
		checkEnvironment: true,
		checkDependencies: true,
		checkDataIntegrity: true,
		checkPerformance: true,
	};

	const checker = new MigrationReadinessChecker(config);

	try {
		const ready = await checker.runAllChecks();
		process.exit(ready ? 0 : 1);
	} catch (error) {
		console.error('\n💥 Readiness check failed:', error);
		process.exit(1);
	} finally {
		await checker.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { MigrationReadinessChecker, ReadinessConfig, CheckResult };
