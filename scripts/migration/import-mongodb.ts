#!/usr/bin/env tsx

import fs from 'fs/promises';
import path from 'path';
import { connectToMongoDB, disconnectFromMongoDB } from '../../src/config/mongodb';
import {
	UserModel,
	WordModel,
	CollectionModel,
	ParagraphModel,
	KeywordModel,
	LastSeenWordModel,
	FeedbackModel,
	CollectionStatsModel,
} from '../../src/backend/schemas';

// Import configuration
interface ImportConfig {
	inputDir: string;
	batchSize: number;
	validateData: boolean;
	dropExisting: boolean;
	createIndexes: boolean;
}

// Import statistics
interface ImportStats {
	inputRecords: number;
	importedRecords: number;
	skippedRecords: number;
	errors: string[];
	startTime: Date;
	endTime?: Date;
	duration?: number;
}

class MongoDBImporter {
	private config: ImportConfig;
	private stats: Map<string, ImportStats> = new Map();

	constructor(config: ImportConfig) {
		this.config = config;
	}

	async initialize(): Promise<void> {
		try {
			await connectToMongoDB();
			console.log('✅ Connected to MongoDB');
			
			if (this.config.dropExisting) {
				console.log('🗑️  Dropping existing collections...');
				await this.dropCollections();
			}
		} catch (error) {
			console.error('❌ Failed to initialize importer:', error);
			throw error;
		}
	}

	async importAllData(): Promise<void> {
		console.log('📥 Starting MongoDB data import...');
		
		try {
			// Import in dependency order (referenced collections first)
			await this.importUsers();
			await this.importWords();
			await this.importCollections();
			await this.importParagraphs();
			await this.importKeywords();
			await this.importLastSeenWords();
			await this.importFeedbacks();
			await this.importCollectionStats();

			if (this.config.createIndexes) {
				await this.createIndexes();
			}

			console.log('\n📊 Import Summary:');
			this.printSummary();
		} catch (error) {
			console.error('❌ Import failed:', error);
			throw error;
		}
	}

	private async dropCollections(): Promise<void> {
		const collections = [
			'users',
			'words',
			'collections',
			'paragraphs',
			'keywords',
			'last_seen_words',
			'feedbacks',
			'collection_stats',
		];

		for (const collectionName of collections) {
			try {
				const model = this.getModelByName(collectionName);
				await model.collection.drop();
				console.log(`   ✅ Dropped ${collectionName}`);
			} catch (error: any) {
				if (error.code === 26) {
					// Collection doesn't exist, which is fine
					console.log(`   ⚠️  Collection ${collectionName} doesn't exist`);
				} else {
					console.error(`   ❌ Error dropping ${collectionName}:`, error);
				}
			}
		}
	}

	private async importUsers(): Promise<void> {
		const collectionName = 'users';
		console.log(`\n📥 Importing ${collectionName}...`);
		
		const stats: ImportStats = {
			inputRecords: 0,
			importedRecords: 0,
			skippedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile('users.json');
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			// Import in batches
			for (let i = 0; i < inputData.length; i += this.config.batchSize) {
				const batch = inputData.slice(i, i + this.config.batchSize);
				
				try {
					const result = await UserModel.insertMany(batch, { 
						ordered: false,
						rawResult: true 
					});
					stats.importedRecords += result.insertedCount;
				} catch (error: any) {
					if (error.writeErrors) {
						stats.importedRecords += batch.length - error.writeErrors.length;
						stats.skippedRecords += error.writeErrors.length;
						for (const writeError of error.writeErrors) {
							stats.errors.push(`User import error: ${writeError.errmsg}`);
						}
					} else {
						stats.errors.push(`Batch import error: ${error.message}`);
						stats.skippedRecords += batch.length;
					}
				}
			}

			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Imported ${stats.importedRecords}/${stats.inputRecords} records`);
			if (stats.skippedRecords > 0) {
				console.log(`   ⚠️  Skipped ${stats.skippedRecords} records`);
			}
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error importing ${collectionName}:`, error);
			throw error;
		}
	}

	private async importWords(): Promise<void> {
		const collectionName = 'words';
		console.log(`\n📥 Importing ${collectionName}...`);
		
		const stats: ImportStats = {
			inputRecords: 0,
			importedRecords: 0,
			skippedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile('words.json');
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			// Import in batches
			for (let i = 0; i < inputData.length; i += this.config.batchSize) {
				const batch = inputData.slice(i, i + this.config.batchSize);
				
				try {
					const result = await WordModel.insertMany(batch, { 
						ordered: false,
						rawResult: true 
					});
					stats.importedRecords += result.insertedCount;
				} catch (error: any) {
					if (error.writeErrors) {
						stats.importedRecords += batch.length - error.writeErrors.length;
						stats.skippedRecords += error.writeErrors.length;
						for (const writeError of error.writeErrors) {
							stats.errors.push(`Word import error: ${writeError.errmsg}`);
						}
					} else {
						stats.errors.push(`Batch import error: ${error.message}`);
						stats.skippedRecords += batch.length;
					}
				}
			}

			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Imported ${stats.importedRecords}/${stats.inputRecords} records`);
			if (stats.skippedRecords > 0) {
				console.log(`   ⚠️  Skipped ${stats.skippedRecords} records`);
			}
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error importing ${collectionName}:`, error);
			throw error;
		}
	}

	private async importCollections(): Promise<void> {
		const collectionName = 'collections';
		console.log(`\n📥 Importing ${collectionName}...`);
		
		const stats: ImportStats = {
			inputRecords: 0,
			importedRecords: 0,
			skippedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile('collections.json');
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			// Import in batches
			for (let i = 0; i < inputData.length; i += this.config.batchSize) {
				const batch = inputData.slice(i, i + this.config.batchSize);
				
				try {
					const result = await CollectionModel.insertMany(batch, { 
						ordered: false,
						rawResult: true 
					});
					stats.importedRecords += result.insertedCount;
				} catch (error: any) {
					if (error.writeErrors) {
						stats.importedRecords += batch.length - error.writeErrors.length;
						stats.skippedRecords += error.writeErrors.length;
						for (const writeError of error.writeErrors) {
							stats.errors.push(`Collection import error: ${writeError.errmsg}`);
						}
					} else {
						stats.errors.push(`Batch import error: ${error.message}`);
						stats.skippedRecords += batch.length;
					}
				}
			}

			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Imported ${stats.importedRecords}/${stats.inputRecords} records`);
			if (stats.skippedRecords > 0) {
				console.log(`   ⚠️  Skipped ${stats.skippedRecords} records`);
			}
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error importing ${collectionName}:`, error);
			throw error;
		}
	}

	private async importParagraphs(): Promise<void> {
		await this.importGenericCollection('paragraphs', ParagraphModel);
	}

	private async importKeywords(): Promise<void> {
		await this.importGenericCollection('keywords', KeywordModel);
	}

	private async importLastSeenWords(): Promise<void> {
		await this.importGenericCollection('last_seen_words', LastSeenWordModel);
	}

	private async importFeedbacks(): Promise<void> {
		await this.importGenericCollection('feedbacks', FeedbackModel);
	}

	private async importCollectionStats(): Promise<void> {
		await this.importGenericCollection('collection_stats', CollectionStatsModel);
	}

	private async importGenericCollection(collectionName: string, model: any): Promise<void> {
		console.log(`\n📥 Importing ${collectionName}...`);
		
		const stats: ImportStats = {
			inputRecords: 0,
			importedRecords: 0,
			skippedRecords: 0,
			errors: [],
			startTime: new Date(),
		};
		this.stats.set(collectionName, stats);

		try {
			const inputData = await this.readInputFile(`${collectionName}.json`);
			stats.inputRecords = inputData.length;
			console.log(`   Input records: ${stats.inputRecords}`);

			// Import in batches
			for (let i = 0; i < inputData.length; i += this.config.batchSize) {
				const batch = inputData.slice(i, i + this.config.batchSize);
				
				try {
					const result = await model.insertMany(batch, { 
						ordered: false,
						rawResult: true 
					});
					stats.importedRecords += result.insertedCount;
				} catch (error: any) {
					if (error.writeErrors) {
						stats.importedRecords += batch.length - error.writeErrors.length;
						stats.skippedRecords += error.writeErrors.length;
						for (const writeError of error.writeErrors) {
							stats.errors.push(`${collectionName} import error: ${writeError.errmsg}`);
						}
					} else {
						stats.errors.push(`Batch import error: ${error.message}`);
						stats.skippedRecords += batch.length;
					}
				}
			}

			stats.endTime = new Date();
			stats.duration = stats.endTime.getTime() - stats.startTime.getTime();
			
			console.log(`   ✅ Imported ${stats.importedRecords}/${stats.inputRecords} records`);
			if (stats.skippedRecords > 0) {
				console.log(`   ⚠️  Skipped ${stats.skippedRecords} records`);
			}
		} catch (error) {
			stats.errors.push(String(error));
			console.error(`   ❌ Error importing ${collectionName}:`, error);
			throw error;
		}
	}

	// Helper methods
	private async readInputFile(filename: string): Promise<any[]> {
		const filePath = path.join(this.config.inputDir, filename);
		const content = await fs.readFile(filePath, 'utf8');
		return JSON.parse(content);
	}

	private getModelByName(collectionName: string): any {
		const modelMap: Record<string, any> = {
			users: UserModel,
			words: WordModel,
			collections: CollectionModel,
			paragraphs: ParagraphModel,
			keywords: KeywordModel,
			last_seen_words: LastSeenWordModel,
			feedbacks: FeedbackModel,
			collection_stats: CollectionStatsModel,
		};
		return modelMap[collectionName];
	}

	private async createIndexes(): Promise<void> {
		console.log('\n🔍 Creating indexes...');
		
		try {
			// Indexes are already defined in schemas, so we just need to ensure they're created
			await UserModel.createIndexes();
			await WordModel.createIndexes();
			await CollectionModel.createIndexes();
			await ParagraphModel.createIndexes();
			await KeywordModel.createIndexes();
			await LastSeenWordModel.createIndexes();
			await FeedbackModel.createIndexes();
			await CollectionStatsModel.createIndexes();
			
			console.log('   ✅ All indexes created successfully');
		} catch (error) {
			console.error('   ❌ Error creating indexes:', error);
			throw error;
		}
	}

	private printSummary(): void {
		let totalInput = 0;
		let totalImported = 0;
		let totalSkipped = 0;
		let totalErrors = 0;

		for (const [collectionName, stats] of this.stats.entries()) {
			totalInput += stats.inputRecords;
			totalImported += stats.importedRecords;
			totalSkipped += stats.skippedRecords;
			totalErrors += stats.errors.length;
			
			console.log(`   ${collectionName}: ${stats.importedRecords}/${stats.inputRecords} (${stats.duration}ms)`);
		}

		console.log(`\n📈 Total: ${totalImported}/${totalInput} records imported`);
		if (totalSkipped > 0) {
			console.log(`⚠️  Total skipped: ${totalSkipped}`);
		}
		if (totalErrors > 0) {
			console.log(`❌ Total errors: ${totalErrors}`);
		}
	}

	async cleanup(): Promise<void> {
		await disconnectFromMongoDB();
		console.log('🔌 Disconnected from MongoDB');
	}
}

// Main execution
async function main() {
	const config: ImportConfig = {
		inputDir: path.join(process.cwd(), 'data', 'mongodb-import'),
		batchSize: 100,
		validateData: true,
		dropExisting: false,
		createIndexes: true,
	};

	const importer = new MongoDBImporter(config);

	try {
		await importer.initialize();
		await importer.importAllData();
		console.log('\n🎉 Import completed successfully!');
	} catch (error) {
		console.error('\n💥 Import failed:', error);
		process.exit(1);
	} finally {
		await importer.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { MongoDBImporter, ImportConfig, ImportStats };
