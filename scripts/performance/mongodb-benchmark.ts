#!/usr/bin/env tsx

import { connectToMongoDB, disconnectFromMongoDB } from '../../src/config/mongodb';
import {
	UserModel,
	WordModel,
	CollectionModel,
	ParagraphModel,
	Provider,
	Language,
	PartsOfSpeech,
	Difficulty,
	Length,
} from '../../src/backend/schemas';

// Benchmark configuration
interface BenchmarkConfig {
	userCount: number;
	wordCount: number;
	collectionCount: number;
	paragraphCount: number;
	iterations: number;
	warmupIterations: number;
}

// Benchmark results
interface BenchmarkResult {
	operation: string;
	avgTime: number;
	minTime: number;
	maxTime: number;
	totalTime: number;
	iterations: number;
	throughput: number; // operations per second
}

class MongoDBBenchmark {
	private config: BenchmarkConfig;
	private results: BenchmarkResult[] = [];

	constructor(config: BenchmarkConfig) {
		this.config = config;
	}

	async initialize(): Promise<void> {
		try {
			await connectToMongoDB();
			console.log('✅ Connected to MongoDB for benchmarking');
			
			// Clear existing data
			await this.clearData();
			
			// Create test data
			await this.createTestData();
		} catch (error) {
			console.error('❌ Failed to initialize benchmark:', error);
			throw error;
		}
	}

	async runAllBenchmarks(): Promise<void> {
		console.log('🚀 Starting MongoDB performance benchmarks...');
		console.log(`📊 Configuration: ${JSON.stringify(this.config, null, 2)}`);
		
		try {
			// User operations
			await this.benchmarkUserOperations();
			
			// Word operations
			await this.benchmarkWordOperations();
			
			// Collection operations
			await this.benchmarkCollectionOperations();
			
			// Complex queries
			await this.benchmarkComplexQueries();
			
			// Aggregation operations
			await this.benchmarkAggregations();
			
			console.log('\n📈 Benchmark Results:');
			this.printResults();
		} catch (error) {
			console.error('❌ Benchmark failed:', error);
			throw error;
		}
	}

	private async clearData(): Promise<void> {
		console.log('🗑️  Clearing existing data...');
		await Promise.all([
			UserModel.deleteMany({}),
			WordModel.deleteMany({}),
			CollectionModel.deleteMany({}),
			ParagraphModel.deleteMany({}),
		]);
	}

	private async createTestData(): Promise<void> {
		console.log('📝 Creating test data...');
		
		// Create users
		const users = Array.from({ length: this.config.userCount }, (_, i) => ({
			provider: i % 2 === 0 ? Provider.TELEGRAM : Provider.USERNAME_PASSWORD,
			provider_id: `user${i}`,
			username: i % 2 === 1 ? `username${i}` : undefined,
			password_hash: i % 2 === 1 ? `hash${i}` : undefined,
		}));
		await UserModel.insertMany(users);
		
		// Create words
		const words = Array.from({ length: this.config.wordCount }, (_, i) => ({
			term: `word${i}`,
			language: i % 2 === 0 ? Language.EN : Language.VI,
			definitions: i % 3 === 0 ? [
				{
					pos: [PartsOfSpeech.NOUN],
					ipa: `/word${i}/`,
					images: [],
					explains: [{ EN: `Definition ${i}`, VI: `Định nghĩa ${i}` }],
					examples: [{ EN: `Example ${i}`, VI: `Ví dụ ${i}` }],
				},
			] : [],
		}));
		await WordModel.insertMany(words);
		
		// Create paragraphs
		const paragraphs = Array.from({ length: this.config.paragraphCount }, (_, i) => ({
			title: `Paragraph ${i}`,
			content: `This is the content of paragraph ${i}. `.repeat(10),
			language: i % 2 === 0 ? Language.EN : Language.VI,
			difficulty: Object.values(Difficulty)[i % 3],
			length: Object.values(Length)[i % 3],
			multiple_choice_exercises: [],
		}));
		await ParagraphModel.insertMany(paragraphs);
		
		console.log(`✅ Created ${this.config.userCount} users, ${this.config.wordCount} words, ${this.config.paragraphCount} paragraphs`);
	}

	private async benchmarkUserOperations(): Promise<void> {
		console.log('\n👥 Benchmarking user operations...');
		
		// Find user by ID
		const users = await UserModel.find().limit(100);
		await this.benchmark('User findById', async () => {
			const randomUser = users[Math.floor(Math.random() * users.length)];
			await UserModel.findById(randomUser._id);
		});
		
		// Find user by provider
		await this.benchmark('User findByProvider', async () => {
			await UserModel.findOne({ provider: Provider.TELEGRAM });
		});
		
		// Create user
		await this.benchmark('User create', async () => {
			const user = new UserModel({
				provider: Provider.TELEGRAM,
				provider_id: `benchmark${Date.now()}${Math.random()}`,
			});
			await user.save();
		});
		
		// Update user
		await this.benchmark('User update', async () => {
			const randomUser = users[Math.floor(Math.random() * users.length)];
			await UserModel.findByIdAndUpdate(randomUser._id, { disabled: !randomUser.disabled });
		});
	}

	private async benchmarkWordOperations(): Promise<void> {
		console.log('\n📚 Benchmarking word operations...');
		
		// Find word by term
		await this.benchmark('Word findByTerm', async () => {
			const randomTerm = `word${Math.floor(Math.random() * this.config.wordCount)}`;
			await WordModel.findOne({ term: randomTerm });
		});
		
		// Search words
		await this.benchmark('Word search', async () => {
			await WordModel.find({ term: { $regex: 'word1', $options: 'i' } }).limit(10);
		});
		
		// Find words with definitions
		await this.benchmark('Word findWithDefinitions', async () => {
			await WordModel.find({ 'definitions.0': { $exists: true } }).limit(10);
		});
		
		// Create word with definitions
		await this.benchmark('Word createWithDefinitions', async () => {
			const word = new WordModel({
				term: `benchmark${Date.now()}${Math.random()}`,
				language: Language.EN,
				definitions: [
					{
						pos: [PartsOfSpeech.NOUN],
						ipa: '/benchmark/',
						images: [],
						explains: [{ EN: 'Benchmark word', VI: 'Từ đánh giá' }],
						examples: [{ EN: 'This is a benchmark', VI: 'Đây là một đánh giá' }],
					},
				],
			});
			await word.save();
		});
	}

	private async benchmarkCollectionOperations(): Promise<void> {
		console.log('\n📁 Benchmarking collection operations...');
		
		const users = await UserModel.find().limit(10);
		const words = await WordModel.find().limit(100);
		
		// Create collection
		await this.benchmark('Collection create', async () => {
			const randomUser = users[Math.floor(Math.random() * users.length)];
			const collection = new CollectionModel({
				name: `Benchmark Collection ${Date.now()}`,
				target_language: Language.EN,
				source_language: Language.VI,
				user_id: randomUser._id,
				word_ids: words.slice(0, 10).map(w => w._id),
				paragraph_ids: [],
				keyword_ids: [],
			});
			await collection.save();
		});
		
		// Find user collections
		await this.benchmark('Collection findByUser', async () => {
			const randomUser = users[Math.floor(Math.random() * users.length)];
			await CollectionModel.find({ user_id: randomUser._id });
		});
	}

	private async benchmarkComplexQueries(): Promise<void> {
		console.log('\n🔍 Benchmarking complex queries...');
		
		// Multi-field search
		await this.benchmark('Complex multiField search', async () => {
			await WordModel.find({
				$and: [
					{ language: Language.EN },
					{ 'definitions.0': { $exists: true } },
					{ term: { $regex: 'word', $options: 'i' } },
				],
			}).limit(10);
		});
		
		// Pagination query
		await this.benchmark('Complex pagination', async () => {
			await WordModel.find({ language: Language.EN })
				.sort({ created_at: -1 })
				.skip(50)
				.limit(20);
		});
		
		// Lookup-like query (manual join)
		await this.benchmark('Complex lookup simulation', async () => {
			const collections = await CollectionModel.find().limit(5);
			for (const collection of collections) {
				await WordModel.find({ _id: { $in: collection.word_ids } });
			}
		});
	}

	private async benchmarkAggregations(): Promise<void> {
		console.log('\n📊 Benchmarking aggregation operations...');
		
		// Count by language
		await this.benchmark('Aggregation countByLanguage', async () => {
			await WordModel.aggregate([
				{
					$group: {
						_id: '$language',
						count: { $sum: 1 },
					},
				},
			]);
		});
		
		// Count by provider
		await this.benchmark('Aggregation countByProvider', async () => {
			await UserModel.aggregate([
				{
					$group: {
						_id: '$provider',
						count: { $sum: 1 },
					},
				},
			]);
		});
		
		// Complex aggregation with multiple stages
		await this.benchmark('Aggregation complex', async () => {
			await WordModel.aggregate([
				{ $match: { language: Language.EN } },
				{
					$addFields: {
						definitionCount: { $size: '$definitions' },
					},
				},
				{
					$group: {
						_id: '$definitionCount',
						words: { $push: '$term' },
						count: { $sum: 1 },
					},
				},
				{ $sort: { count: -1 } },
				{ $limit: 10 },
			]);
		});
	}

	private async benchmark(operation: string, fn: () => Promise<void>): Promise<void> {
		const times: number[] = [];
		
		// Warmup
		for (let i = 0; i < this.config.warmupIterations; i++) {
			await fn();
		}
		
		// Actual benchmark
		for (let i = 0; i < this.config.iterations; i++) {
			const start = process.hrtime.bigint();
			await fn();
			const end = process.hrtime.bigint();
			const duration = Number(end - start) / 1_000_000; // Convert to milliseconds
			times.push(duration);
		}
		
		const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
		const minTime = Math.min(...times);
		const maxTime = Math.max(...times);
		const totalTime = times.reduce((a, b) => a + b, 0);
		const throughput = (this.config.iterations / totalTime) * 1000; // ops per second
		
		const result: BenchmarkResult = {
			operation,
			avgTime,
			minTime,
			maxTime,
			totalTime,
			iterations: this.config.iterations,
			throughput,
		};
		
		this.results.push(result);
		console.log(`   ${operation}: ${avgTime.toFixed(2)}ms avg (${throughput.toFixed(2)} ops/sec)`);
	}

	private printResults(): void {
		console.log('\n' + '='.repeat(80));
		console.log('📈 DETAILED BENCHMARK RESULTS');
		console.log('='.repeat(80));
		
		for (const result of this.results) {
			console.log(`\n${result.operation}:`);
			console.log(`  Average: ${result.avgTime.toFixed(2)}ms`);
			console.log(`  Min: ${result.minTime.toFixed(2)}ms`);
			console.log(`  Max: ${result.maxTime.toFixed(2)}ms`);
			console.log(`  Throughput: ${result.throughput.toFixed(2)} ops/sec`);
			console.log(`  Iterations: ${result.iterations}`);
		}
		
		// Summary
		console.log('\n' + '='.repeat(80));
		console.log('📊 SUMMARY');
		console.log('='.repeat(80));
		
		const avgThroughput = this.results.reduce((sum, r) => sum + r.throughput, 0) / this.results.length;
		const fastestOp = this.results.reduce((min, r) => r.avgTime < min.avgTime ? r : min);
		const slowestOp = this.results.reduce((max, r) => r.avgTime > max.avgTime ? r : max);
		
		console.log(`Average throughput: ${avgThroughput.toFixed(2)} ops/sec`);
		console.log(`Fastest operation: ${fastestOp.operation} (${fastestOp.avgTime.toFixed(2)}ms)`);
		console.log(`Slowest operation: ${slowestOp.operation} (${slowestOp.avgTime.toFixed(2)}ms)`);
	}

	async cleanup(): Promise<void> {
		await disconnectFromMongoDB();
		console.log('🔌 Disconnected from MongoDB');
	}
}

// Main execution
async function main() {
	const config: BenchmarkConfig = {
		userCount: 1000,
		wordCount: 5000,
		collectionCount: 100,
		paragraphCount: 500,
		iterations: 100,
		warmupIterations: 10,
	};

	const benchmark = new MongoDBBenchmark(config);

	try {
		await benchmark.initialize();
		await benchmark.runAllBenchmarks();
		console.log('\n🎉 Benchmark completed successfully!');
	} catch (error) {
		console.error('\n💥 Benchmark failed:', error);
		process.exit(1);
	} finally {
		await benchmark.cleanup();
	}
}

// Run if called directly
if (require.main === module) {
	main().catch(console.error);
}

export { MongoDBBenchmark, BenchmarkConfig, BenchmarkResult };
