// Environment setup for tests
process.env.NODE_ENV = 'test';

// MongoDB test configuration
process.env.FEATURE_MONGODB_ENABLED = 'true';
process.env.FEATURE_DUAL_DATABASE = 'false';

// Disable other features for testing
process.env.FEATURE_GOOGLE_LOGIN = 'false';

// Test database URLs (will be overridden by in-memory MongoDB)
process.env.MONGODB_URI = 'mongodb://localhost:27017/vocab-test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/vocab-test';

// JWT secret for testing
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';

// Disable logging in tests
process.env.LOG_LEVEL = 'error';

// OpenAI API key for testing (mock)
process.env.OPENAI_API_KEY = 'test-openai-api-key';

console.log('🧪 Test environment configured');
