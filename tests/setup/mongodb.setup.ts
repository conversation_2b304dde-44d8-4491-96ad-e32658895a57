import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';

// Global MongoDB setup for tests
let mongoServer: MongoMemoryServer;

export const setupMongoDB = async (): Promise<void> => {
	// Start in-memory MongoDB instance
	mongoServer = await MongoMemoryServer.create({
		binary: {
			version: '7.0.0',
		},
		instance: {
			dbName: 'vocab-test',
		},
	});

	const mongoUri = mongoServer.getUri();
	
	// Connect to the in-memory database
	await mongoose.connect(mongoUri, {
		bufferCommands: false,
		bufferMaxEntries: 0,
	});

	console.log('✅ Connected to in-memory MongoDB for testing');
};

export const teardownMongoDB = async (): Promise<void> => {
	// Disconnect from MongoDB
	await mongoose.disconnect();
	
	// Stop the in-memory MongoDB instance
	if (mongoServer) {
		await mongoServer.stop();
	}

	console.log('🔌 Disconnected from in-memory MongoDB');
};

export const clearDatabase = async (): Promise<void> => {
	if (mongoose.connection.readyState === 1) {
		const collections = mongoose.connection.collections;
		
		for (const key in collections) {
			const collection = collections[key];
			await collection.deleteMany({});
		}
	}
};

export const getMongoUri = (): string => {
	if (!mongoServer) {
		throw new Error('MongoDB server not initialized');
	}
	return mongoServer.getUri();
};

// Jest global setup and teardown
export default async (): Promise<void> => {
	await setupMongoDB();
	
	// Store the teardown function globally
	(global as any).__TEARDOWN_MONGODB__ = teardownMongoDB;
};

// Jest global teardown
export const globalTeardown = async (): Promise<void> => {
	const teardown = (global as any).__TEARDOWN_MONGODB__;
	if (teardown) {
		await teardown();
	}
};
