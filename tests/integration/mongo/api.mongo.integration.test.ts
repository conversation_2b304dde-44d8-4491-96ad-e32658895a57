import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { createMocks } from 'node-mocks-http';
import { NextRequest, NextResponse } from 'next/server';

// Import API handlers
import { POST as createUser } from '@/app/api/users/route';
import { POST as createWord } from '@/app/api/words/route';

// Import models and types
import { UserModel, WordModel, Provider, Language } from '@/backend/schemas';

// Mock environment variables
process.env.FEATURE_MONGODB_ENABLED = 'true';
process.env.FEATURE_DUAL_DATABASE = 'false';
process.env.JWT_SECRET = 'test-jwt-secret';

describe('API Integration Tests with MongoDB', () => {
	let mongoServer: MongoMemoryServer;

	beforeAll(async () => {
		// Start in-memory MongoDB instance
		mongoServer = await MongoMemoryServer.create();
		const mongoUri = mongoServer.getUri();
		
		// Connect to the in-memory database
		await mongoose.connect(mongoUri);
		
		console.log('✅ Connected to in-memory MongoDB for integration tests');
	});

	afterAll(async () => {
		// Cleanup
		await mongoose.disconnect();
		await mongoServer.stop();
		console.log('🔌 Disconnected from in-memory MongoDB');
	});

	beforeEach(async () => {
		// Clear the database before each test
		await UserModel.deleteMany({});
		await WordModel.deleteMany({});
	});

	describe('User API', () => {
		it('should create a new user via API', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
				password_hash: 'hashedpassword',
			};

			// Create mock request
			const request = new NextRequest('http://localhost:3000/api/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(userData),
			});

			// Call API handler
			const response = await createUser(request);
			const responseData = await response.json();

			expect(response.status).toBe(201);
			expect(responseData.success).toBe(true);
			expect(responseData.data.provider).toBe(Provider.USERNAME_PASSWORD);
			expect(responseData.data.username).toBe('testuser');

			// Verify user was created in database
			const userInDb = await UserModel.findOne({ provider_id: 'test123' });
			expect(userInDb).toBeTruthy();
			expect(userInDb!.username).toBe('testuser');
		});

		it('should handle validation errors', async () => {
			const invalidUserData = {
				provider: Provider.USERNAME_PASSWORD,
				// Missing required fields
			};

			const request = new NextRequest('http://localhost:3000/api/users', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(invalidUserData),
			});

			const response = await createUser(request);
			const responseData = await response.json();

			expect(response.status).toBe(400);
			expect(responseData.success).toBe(false);
			expect(responseData.error).toBeDefined();
		});
	});

	describe('Word API', () => {
		it('should create a new word via API', async () => {
			const wordData = {
				term: 'hello',
				language: Language.EN,
				definitions: [
					{
						pos: ['NOUN'],
						ipa: '/həˈloʊ/',
						images: [],
						explains: [
							{ EN: 'A greeting', VI: 'Lời chào' }
						],
						examples: [
							{ EN: 'Hello, how are you?', VI: 'Xin chào, bạn khỏe không?' }
						],
					},
				],
			};

			const request = new NextRequest('http://localhost:3000/api/words', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(wordData),
			});

			const response = await createWord(request);
			const responseData = await response.json();

			expect(response.status).toBe(201);
			expect(responseData.success).toBe(true);
			expect(responseData.data.term).toBe('hello');
			expect(responseData.data.language).toBe(Language.EN);
			expect(responseData.data.definitions).toHaveLength(1);

			// Verify word was created in database
			const wordInDb = await WordModel.findOne({ term: 'hello', language: Language.EN });
			expect(wordInDb).toBeTruthy();
			expect(wordInDb!.definitions).toHaveLength(1);
			expect(wordInDb!.definitions[0].explains).toHaveLength(1);
		});

		it('should handle duplicate word creation', async () => {
			// Create first word
			const wordData = {
				term: 'duplicate',
				language: Language.EN,
				definitions: [],
			};

			await WordModel.create(wordData);

			// Try to create duplicate
			const request = new NextRequest('http://localhost:3000/api/words', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(wordData),
			});

			const response = await createWord(request);
			const responseData = await response.json();

			expect(response.status).toBe(409);
			expect(responseData.success).toBe(false);
			expect(responseData.error).toContain('already exists');
		});
	});

	describe('Database Connection', () => {
		it('should be connected to MongoDB', async () => {
			expect(mongoose.connection.readyState).toBe(1); // 1 = connected
		});

		it('should be able to perform basic CRUD operations', async () => {
			// Create
			const user = await UserModel.create({
				provider: Provider.TELEGRAM,
				provider_id: 'telegram123',
			});

			expect(user).toBeTruthy();
			expect(user.provider_id).toBe('telegram123');

			// Read
			const foundUser = await UserModel.findById(user._id);
			expect(foundUser).toBeTruthy();
			expect(foundUser!.provider_id).toBe('telegram123');

			// Update
			const updatedUser = await UserModel.findByIdAndUpdate(
				user._id,
				{ disabled: true },
				{ new: true }
			);
			expect(updatedUser!.disabled).toBe(true);

			// Delete
			await UserModel.findByIdAndDelete(user._id);
			const deletedUser = await UserModel.findById(user._id);
			expect(deletedUser).toBeNull();
		});
	});

	describe('Complex Queries', () => {
		beforeEach(async () => {
			// Create test data
			await UserModel.create([
				{
					provider: Provider.TELEGRAM,
					provider_id: 'telegram1',
				},
				{
					provider: Provider.USERNAME_PASSWORD,
					provider_id: 'user1',
					username: 'testuser1',
					password_hash: 'hash1',
				},
				{
					provider: Provider.GOOGLE,
					provider_id: 'google1',
				},
			]);

			await WordModel.create([
				{
					term: 'hello',
					language: Language.EN,
					definitions: [
						{
							pos: ['NOUN'],
							ipa: '/həˈloʊ/',
							images: [],
							explains: [{ EN: 'A greeting', VI: 'Lời chào' }],
							examples: [],
						},
					],
				},
				{
					term: 'world',
					language: Language.EN,
					definitions: [],
				},
				{
					term: 'xin chào',
					language: Language.VI,
					definitions: [],
				},
			]);
		});

		it('should perform aggregation queries', async () => {
			const userCounts = await UserModel.aggregate([
				{
					$group: {
						_id: '$provider',
						count: { $sum: 1 },
					},
				},
			]);

			expect(userCounts).toHaveLength(3);
			expect(userCounts.find(c => c._id === Provider.TELEGRAM)?.count).toBe(1);
			expect(userCounts.find(c => c._id === Provider.USERNAME_PASSWORD)?.count).toBe(1);
			expect(userCounts.find(c => c._id === Provider.GOOGLE)?.count).toBe(1);
		});

		it('should perform text search queries', async () => {
			// Create text index
			await WordModel.createIndexes();

			const searchResults = await WordModel.find({
				term: { $regex: 'hello', $options: 'i' },
			});

			expect(searchResults).toHaveLength(1);
			expect(searchResults[0].term).toBe('hello');
		});

		it('should handle embedded document queries', async () => {
			const wordsWithDefinitions = await WordModel.find({
				'definitions.0': { $exists: true },
			});

			expect(wordsWithDefinitions).toHaveLength(1);
			expect(wordsWithDefinitions[0].term).toBe('hello');
		});

		it('should perform language-specific queries', async () => {
			const englishWords = await WordModel.find({ language: Language.EN });
			const vietnameseWords = await WordModel.find({ language: Language.VI });

			expect(englishWords).toHaveLength(2);
			expect(vietnameseWords).toHaveLength(1);
			expect(vietnameseWords[0].term).toBe('xin chào');
		});
	});

	describe('Performance Tests', () => {
		it('should handle bulk operations efficiently', async () => {
			const startTime = Date.now();

			// Create 100 users
			const users = Array.from({ length: 100 }, (_, i) => ({
				provider: Provider.TELEGRAM,
				provider_id: `telegram${i}`,
			}));

			await UserModel.insertMany(users);

			const endTime = Date.now();
			const duration = endTime - startTime;

			// Should complete within reasonable time (adjust threshold as needed)
			expect(duration).toBeLessThan(1000); // 1 second

			// Verify all users were created
			const userCount = await UserModel.countDocuments();
			expect(userCount).toBe(100);
		});

		it('should handle concurrent operations', async () => {
			const promises = Array.from({ length: 10 }, (_, i) =>
				UserModel.create({
					provider: Provider.TELEGRAM,
					provider_id: `concurrent${i}`,
				})
			);

			const results = await Promise.all(promises);

			expect(results).toHaveLength(10);
			results.forEach((user, index) => {
				expect(user.provider_id).toBe(`concurrent${index}`);
			});
		});
	});
});
