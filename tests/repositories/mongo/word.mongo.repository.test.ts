import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { WordMongoRepositoryImpl } from '@/backend/repositories/word.mongo.repository';
import { WordModel, Language, PartsOfSpeech } from '@/backend/schemas';

describe('WordMongoRepositoryImpl', () => {
	let mongoServer: MongoMemoryServer;
	let wordRepository: WordMongoRepositoryImpl;

	beforeAll(async () => {
		// Start in-memory MongoDB instance
		mongoServer = await MongoMemoryServer.create();
		const mongoUri = mongoServer.getUri();
		
		// Connect to the in-memory database
		await mongoose.connect(mongoUri);
		
		// Initialize repository
		wordRepository = new WordMongoRepositoryImpl();
	});

	afterAll(async () => {
		// Cleanup
		await mongoose.disconnect();
		await mongoServer.stop();
	});

	beforeEach(async () => {
		// Clear the database before each test
		await WordModel.deleteMany({});
	});

	afterEach(async () => {
		// Clear the database after each test
		await WordModel.deleteMany({});
	});

	describe('create', () => {
		it('should create a new word with valid data', async () => {
			const wordData = {
				term: 'hello',
				language: Language.EN,
				definitions: [],
			};

			const word = await wordRepository.create(wordData);

			expect(word).toBeDefined();
			expect(word.term).toBe('hello');
			expect(word.language).toBe(Language.EN);
			expect(word.definitions).toEqual([]);
			expect(word.created_at).toBeDefined();
			expect(word.updated_at).toBeDefined();
		});

		it('should create a word with definitions', async () => {
			const wordData = {
				term: 'book',
				language: Language.EN,
				definitions: [
					{
						pos: [PartsOfSpeech.NOUN],
						ipa: '/bʊk/',
						images: [],
						explains: [
							{ EN: 'A written work', VI: 'Một tác phẩm viết' }
						],
						examples: [
							{ EN: 'I read a book', VI: 'Tôi đọc một cuốn sách' }
						],
					},
				],
			};

			const word = await wordRepository.create(wordData);

			expect(word).toBeDefined();
			expect(word.definitions).toHaveLength(1);
			expect(word.definitions[0].pos).toContain(PartsOfSpeech.NOUN);
			expect(word.definitions[0].ipa).toBe('/bʊk/');
			expect(word.definitions[0].explains).toHaveLength(1);
			expect(word.definitions[0].examples).toHaveLength(1);
		});

		it('should throw error when creating word without term', async () => {
			const wordData = {
				language: Language.EN,
			};

			await expect(wordRepository.create(wordData)).rejects.toThrow('Term and language are required');
		});

		it('should throw error when creating word without language', async () => {
			const wordData = {
				term: 'hello',
			};

			await expect(wordRepository.create(wordData)).rejects.toThrow('Term and language are required');
		});
	});

	describe('findById', () => {
		it('should find word by ID', async () => {
			const wordData = {
				term: 'test',
				language: Language.EN,
				definitions: [],
			};

			const createdWord = await wordRepository.create(wordData);
			const foundWord = await wordRepository.findById(createdWord.id);

			expect(foundWord).toBeDefined();
			expect(foundWord!.id).toBe(createdWord.id);
			expect(foundWord!.term).toBe('test');
		});

		it('should return null for non-existent ID', async () => {
			const nonExistentId = new mongoose.Types.ObjectId().toString();
			const foundWord = await wordRepository.findById(nonExistentId);

			expect(foundWord).toBeNull();
		});
	});

	describe('findByTerm', () => {
		it('should find word by term and language', async () => {
			const wordData = {
				term: 'hello',
				language: Language.EN,
				definitions: [],
			};

			await wordRepository.create(wordData);
			const foundWord = await wordRepository.findByTerm('hello', Language.EN);

			expect(foundWord).toBeDefined();
			expect(foundWord!.term).toBe('hello');
			expect(foundWord!.language).toBe(Language.EN);
		});

		it('should return null for non-existent term', async () => {
			const foundWord = await wordRepository.findByTerm('nonexistent', Language.EN);

			expect(foundWord).toBeNull();
		});

		it('should return null for same term but different language', async () => {
			await wordRepository.create({
				term: 'hello',
				language: Language.EN,
				definitions: [],
			});

			const foundWord = await wordRepository.findByTerm('hello', Language.VI);

			expect(foundWord).toBeNull();
		});
	});

	describe('searchWords', () => {
		beforeEach(async () => {
			// Create test words
			await wordRepository.create({
				term: 'hello',
				language: Language.EN,
				definitions: [],
			});

			await wordRepository.create({
				term: 'world',
				language: Language.EN,
				definitions: [],
			});

			await wordRepository.create({
				term: 'xin chào',
				language: Language.VI,
				definitions: [],
			});
		});

		it('should search words by term (fallback to regex)', async () => {
			const results = await wordRepository.searchWords('hello');

			expect(results).toHaveLength(1);
			expect(results[0].term).toBe('hello');
		});

		it('should search words by language', async () => {
			const results = await wordRepository.searchWords('o', Language.EN);

			expect(results).toHaveLength(2);
			expect(results.every(word => word.language === Language.EN)).toBe(true);
		});

		it('should respect limit parameter', async () => {
			const results = await wordRepository.searchWords('o', Language.EN, 1);

			expect(results).toHaveLength(1);
		});
	});

	describe('findOrCreateWords', () => {
		it('should find existing words', async () => {
			await wordRepository.create({
				term: 'existing',
				language: Language.EN,
				definitions: [],
			});

			const words = await wordRepository.findOrCreateWords(['existing'], Language.EN);

			expect(words).toHaveLength(1);
			expect(words[0].term).toBe('existing');
		});

		it('should create new words', async () => {
			const words = await wordRepository.findOrCreateWords(['new1', 'new2'], Language.EN);

			expect(words).toHaveLength(2);
			expect(words[0].term).toBe('new1');
			expect(words[1].term).toBe('new2');
		});

		it('should mix existing and new words', async () => {
			await wordRepository.create({
				term: 'existing',
				language: Language.EN,
				definitions: [],
			});

			const words = await wordRepository.findOrCreateWords(['existing', 'new'], Language.EN);

			expect(words).toHaveLength(2);
			expect(words.some(word => word.term === 'existing')).toBe(true);
			expect(words.some(word => word.term === 'new')).toBe(true);
		});
	});

	describe('findWordsByIds', () => {
		it('should find words by array of IDs', async () => {
			const word1 = await wordRepository.create({
				term: 'word1',
				language: Language.EN,
				definitions: [],
			});

			const word2 = await wordRepository.create({
				term: 'word2',
				language: Language.EN,
				definitions: [],
			});

			const words = await wordRepository.findWordsByIds([word1.id, word2.id]);

			expect(words).toHaveLength(2);
			expect(words.some(word => word.id === word1.id)).toBe(true);
			expect(words.some(word => word.id === word2.id)).toBe(true);
		});

		it('should return empty array for non-existent IDs', async () => {
			const nonExistentId = new mongoose.Types.ObjectId().toString();
			const words = await wordRepository.findWordsByIds([nonExistentId]);

			expect(words).toHaveLength(0);
		});
	});

	describe('addDefinitionToWord', () => {
		it('should add definition to word', async () => {
			const word = await wordRepository.create({
				term: 'test',
				language: Language.EN,
				definitions: [],
			});

			const definition = {
				pos: [PartsOfSpeech.NOUN],
				ipa: '/test/',
				images: [],
				explains: [],
				examples: [],
			};

			const updatedWord = await wordRepository.addDefinitionToWord(word.id, definition);

			expect(updatedWord.definitions).toHaveLength(1);
			expect(updatedWord.definitions[0].pos).toContain(PartsOfSpeech.NOUN);
			expect(updatedWord.definitions[0].ipa).toBe('/test/');
		});

		it('should throw error for non-existent word', async () => {
			const nonExistentId = new mongoose.Types.ObjectId().toString();
			const definition = {
				pos: [PartsOfSpeech.NOUN],
				ipa: '/test/',
				images: [],
				explains: [],
				examples: [],
			};

			await expect(wordRepository.addDefinitionToWord(nonExistentId, definition)).rejects.toThrow('Word not found');
		});
	});

	describe('updateDefinition', () => {
		it('should update existing definition', async () => {
			const word = await wordRepository.create({
				term: 'test',
				language: Language.EN,
				definitions: [
					{
						pos: [PartsOfSpeech.NOUN],
						ipa: '/test/',
						images: [],
						explains: [],
						examples: [],
					},
				],
			});

			const definitionId = word.definitions[0]._id.toString();
			const update = { ipa: '/updated/' };

			const updatedWord = await wordRepository.updateDefinition(word.id, definitionId, update);

			expect(updatedWord.definitions[0].ipa).toBe('/updated/');
		});

		it('should throw error for non-existent definition', async () => {
			const word = await wordRepository.create({
				term: 'test',
				language: Language.EN,
				definitions: [],
			});

			const nonExistentDefinitionId = new mongoose.Types.ObjectId().toString();

			await expect(wordRepository.updateDefinition(word.id, nonExistentDefinitionId, { ipa: '/test/' })).rejects.toThrow('Definition not found');
		});
	});

	describe('removeDefinition', () => {
		it('should remove definition from word', async () => {
			const word = await wordRepository.create({
				term: 'test',
				language: Language.EN,
				definitions: [
					{
						pos: [PartsOfSpeech.NOUN],
						ipa: '/test/',
						images: [],
						explains: [],
						examples: [],
					},
				],
			});

			const definitionId = word.definitions[0]._id.toString();
			const updatedWord = await wordRepository.removeDefinition(word.id, definitionId);

			expect(updatedWord.definitions).toHaveLength(0);
		});
	});

	describe('countWordsByLanguage', () => {
		beforeEach(async () => {
			// Create test words
			await wordRepository.create({
				term: 'hello',
				language: Language.EN,
				definitions: [],
			});

			await wordRepository.create({
				term: 'world',
				language: Language.EN,
				definitions: [],
			});

			await wordRepository.create({
				term: 'xin chào',
				language: Language.VI,
				definitions: [],
			});
		});

		it('should count words by language', async () => {
			const counts = await wordRepository.countWordsByLanguage();

			expect(counts[Language.EN]).toBe(2);
			expect(counts[Language.VI]).toBe(1);
		});
	});
});
