import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { MongoMemoryServer } from 'mongodb-memory-server';
import mongoose from 'mongoose';
import { UserMongoRepositoryImpl } from '@/backend/repositories/user.mongo.repository';
import { UserModel, Provider } from '@/backend/schemas';

describe('UserMongoRepositoryImpl', () => {
	let mongoServer: MongoMemoryServer;
	let userRepository: UserMongoRepositoryImpl;

	beforeAll(async () => {
		// Start in-memory MongoDB instance
		mongoServer = await MongoMemoryServer.create();
		const mongoUri = mongoServer.getUri();
		
		// Connect to the in-memory database
		await mongoose.connect(mongoUri);
		
		// Initialize repository
		userRepository = new UserMongoRepositoryImpl();
	});

	afterAll(async () => {
		// Cleanup
		await mongoose.disconnect();
		await mongoServer.stop();
	});

	beforeEach(async () => {
		// Clear the database before each test
		await UserModel.deleteMany({});
	});

	afterEach(async () => {
		// Clear the database after each test
		await UserModel.deleteMany({});
	});

	describe('create', () => {
		it('should create a new user with valid data', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
				password_hash: 'hashedpassword',
				disabled: false,
			};

			const user = await userRepository.create(userData);

			expect(user).toBeDefined();
			expect(user.provider).toBe(Provider.USERNAME_PASSWORD);
			expect(user.provider_id).toBe('test123');
			expect(user.username).toBe('testuser');
			expect(user.disabled).toBe(false);
			expect(user.created_at).toBeDefined();
			expect(user.updated_at).toBeDefined();
		});

		it('should create a user without username for non-USERNAME_PASSWORD provider', async () => {
			const userData = {
				provider: Provider.TELEGRAM,
				provider_id: 'telegram123',
				disabled: false,
			};

			const user = await userRepository.create(userData);

			expect(user).toBeDefined();
			expect(user.provider).toBe(Provider.TELEGRAM);
			expect(user.provider_id).toBe('telegram123');
			expect(user.username).toBeUndefined();
		});

		it('should throw error when creating USERNAME_PASSWORD user without username', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				password_hash: 'hashedpassword',
			};

			await expect(userRepository.create(userData)).rejects.toThrow('Username is required for USERNAME_PASSWORD provider');
		});

		it('should throw error when creating USERNAME_PASSWORD user without password_hash', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
			};

			await expect(userRepository.create(userData)).rejects.toThrow('Password hash is required for USERNAME_PASSWORD provider');
		});

		it('should throw error when creating user without provider_id', async () => {
			const userData = {
				provider: Provider.TELEGRAM,
			};

			await expect(userRepository.create(userData)).rejects.toThrow('Provider ID is required');
		});
	});

	describe('findById', () => {
		it('should find user by ID', async () => {
			const userData = {
				provider: Provider.TELEGRAM,
				provider_id: 'telegram123',
			};

			const createdUser = await userRepository.create(userData);
			const foundUser = await userRepository.findById(createdUser.id);

			expect(foundUser).toBeDefined();
			expect(foundUser!.id).toBe(createdUser.id);
			expect(foundUser!.provider).toBe(Provider.TELEGRAM);
		});

		it('should return null for non-existent ID', async () => {
			const nonExistentId = new mongoose.Types.ObjectId().toString();
			const foundUser = await userRepository.findById(nonExistentId);

			expect(foundUser).toBeNull();
		});
	});

	describe('findByProviderId', () => {
		it('should find user by provider and provider_id', async () => {
			const userData = {
				provider: Provider.TELEGRAM,
				provider_id: 'telegram123',
			};

			await userRepository.create(userData);
			const foundUser = await userRepository.findByProviderId(Provider.TELEGRAM, 'telegram123');

			expect(foundUser).toBeDefined();
			expect(foundUser!.provider).toBe(Provider.TELEGRAM);
			expect(foundUser!.provider_id).toBe('telegram123');
		});

		it('should return null for non-existent provider_id', async () => {
			const foundUser = await userRepository.findByProviderId(Provider.TELEGRAM, 'nonexistent');

			expect(foundUser).toBeNull();
		});
	});

	describe('findByUsername', () => {
		it('should find user by username', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
				password_hash: 'hashedpassword',
			};

			await userRepository.create(userData);
			const foundUser = await userRepository.findByUsername('testuser');

			expect(foundUser).toBeDefined();
			expect(foundUser!.username).toBe('testuser');
		});

		it('should return null for non-existent username', async () => {
			const foundUser = await userRepository.findByUsername('nonexistent');

			expect(foundUser).toBeNull();
		});
	});

	describe('searchUsers', () => {
		beforeEach(async () => {
			// Create test users
			await userRepository.create({
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'user1',
				username: 'john_doe',
				password_hash: 'hash1',
			});

			await userRepository.create({
				provider: Provider.TELEGRAM,
				provider_id: 'telegram_john',
			});

			await userRepository.create({
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'user2',
				username: 'jane_smith',
				password_hash: 'hash2',
			});
		});

		it('should search users by username', async () => {
			const results = await userRepository.searchUsers('john');

			expect(results).toHaveLength(2);
			expect(results.some(user => user.username === 'john_doe')).toBe(true);
			expect(results.some(user => user.provider_id === 'telegram_john')).toBe(true);
		});

		it('should search users by provider_id', async () => {
			const results = await userRepository.searchUsers('telegram');

			expect(results).toHaveLength(1);
			expect(results[0].provider_id).toBe('telegram_john');
		});

		it('should respect limit parameter', async () => {
			const results = await userRepository.searchUsers('user', 1);

			expect(results).toHaveLength(1);
		});
	});

	describe('update', () => {
		it('should update user data', async () => {
			const userData = {
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
				password_hash: 'hashedpassword',
			};

			const createdUser = await userRepository.create(userData);
			const updatedUser = await userRepository.update(createdUser.id, {
				username: 'updateduser',
				disabled: true,
			});

			expect(updatedUser.username).toBe('updateduser');
			expect(updatedUser.disabled).toBe(true);
			expect(updatedUser.updated_at.getTime()).toBeGreaterThan(updatedUser.created_at.getTime());
		});

		it('should throw error when updating non-existent user', async () => {
			const nonExistentId = new mongoose.Types.ObjectId().toString();

			await expect(userRepository.update(nonExistentId, { username: 'test' })).rejects.toThrow('Document not found');
		});
	});

	describe('disableUser and enableUser', () => {
		it('should disable and enable user', async () => {
			const userData = {
				provider: Provider.TELEGRAM,
				provider_id: 'telegram123',
			};

			const createdUser = await userRepository.create(userData);
			
			// Disable user
			const disabledUser = await userRepository.disableUser(createdUser.id);
			expect(disabledUser.disabled).toBe(true);

			// Enable user
			const enabledUser = await userRepository.enableUser(createdUser.id);
			expect(enabledUser.disabled).toBe(false);
		});
	});

	describe('isUsernameAvailable', () => {
		it('should return true for available username', async () => {
			const isAvailable = await userRepository.isUsernameAvailable('newuser');
			expect(isAvailable).toBe(true);
		});

		it('should return false for taken username', async () => {
			await userRepository.create({
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'takenuser',
				password_hash: 'hash',
			});

			const isAvailable = await userRepository.isUsernameAvailable('takenuser');
			expect(isAvailable).toBe(false);
		});

		it('should exclude specific user ID when checking availability', async () => {
			const user = await userRepository.create({
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'test123',
				username: 'testuser',
				password_hash: 'hash',
			});

			const isAvailable = await userRepository.isUsernameAvailable('testuser', user.id);
			expect(isAvailable).toBe(true);
		});
	});

	describe('isProviderIdAvailable', () => {
		it('should return true for available provider ID', async () => {
			const isAvailable = await userRepository.isProviderIdAvailable(Provider.TELEGRAM, 'new_telegram_id');
			expect(isAvailable).toBe(true);
		});

		it('should return false for taken provider ID', async () => {
			await userRepository.create({
				provider: Provider.TELEGRAM,
				provider_id: 'taken_telegram_id',
			});

			const isAvailable = await userRepository.isProviderIdAvailable(Provider.TELEGRAM, 'taken_telegram_id');
			expect(isAvailable).toBe(false);
		});
	});

	describe('countUsersByProvider', () => {
		beforeEach(async () => {
			// Create test users with different providers
			await userRepository.create({
				provider: Provider.TELEGRAM,
				provider_id: 'telegram1',
			});

			await userRepository.create({
				provider: Provider.TELEGRAM,
				provider_id: 'telegram2',
			});

			await userRepository.create({
				provider: Provider.USERNAME_PASSWORD,
				provider_id: 'user1',
				username: 'user1',
				password_hash: 'hash1',
			});

			await userRepository.create({
				provider: Provider.GOOGLE,
				provider_id: 'google1',
			});
		});

		it('should count users by provider', async () => {
			const counts = await userRepository.countUsersByProvider();

			expect(counts[Provider.TELEGRAM]).toBe(2);
			expect(counts[Provider.USERNAME_PASSWORD]).toBe(1);
			expect(counts[Provider.GOOGLE]).toBe(1);
		});
	});
});
