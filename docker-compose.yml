services:
    postgresql:
        image: postgres:latest
        environment:
            - POSTGRES_DB=vocab
            - POSTGRES_USER=postgres
            - POSTGRES_PASSWORD=postgres
        ports:
            - '5432:5432'
        volumes:
            - postgresql_data:/var/lib/postgresql/data
        networks:
            - vocab-network

    mongodb:
        image: mongo:latest
        environment:
            - MONGO_INITDB_DATABASE=vocab
        ports:
            - '27017:27017'
        volumes:
            - mongodb_data:/data/db
        networks:
            - vocab-network

networks:
    vocab-network:
        driver: bridge

volumes:
    postgresql_data:
    mongodb_data:
