'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';

// ============================================================================
// SIMPLE FLOATING UI TYPES
// ============================================================================

export interface SimpleFloatingElement {
	id: string;
	content: ReactNode;
	visible: boolean;
	position: {
		top?: number;
		bottom?: number;
		left?: number;
		right?: number;
	};
	zIndex?: number;
	className?: string;
	style?: React.CSSProperties;
}

interface SimpleFloatingContextType {
	elements: Map<string, SimpleFloatingElement>;
	register: (element: Omit<SimpleFloatingElement, 'visible'>) => void;
	unregister: (id: string) => void;
	show: (id: string) => void;
	hide: (id: string) => void;
	toggle: (id: string) => void;
	update: (id: string, updates: Partial<SimpleFloatingElement>) => void;
	hideAll: () => void;
	getVisibleElements: () => SimpleFloatingElement[];
}

// ============================================================================
// SIMPLE FLOATING UI CONTEXT
// ============================================================================

const SimpleFloatingContext = createContext<SimpleFloatingContextType | null>(null);

interface SimpleFloatingProviderProps {
	children: ReactNode;
}

export function SimpleFloatingProvider({ children }: SimpleFloatingProviderProps) {
	const [elements, setElements] = useState<Map<string, SimpleFloatingElement>>(new Map());

	const register = useCallback((element: Omit<SimpleFloatingElement, 'visible'>) => {
		setElements((prev) => {
			const newElements = new Map(prev);
			newElements.set(element.id, { ...element, visible: false });
			return newElements;
		});
	}, []);

	const unregister = useCallback((id: string) => {
		setElements((prev) => {
			const newElements = new Map(prev);
			newElements.delete(id);
			return newElements;
		});
	}, []);

	const show = useCallback((id: string) => {
		setElements((prev) => {
			const element = prev.get(id);
			if (!element) return prev;

			const newElements = new Map(prev);
			newElements.set(id, { ...element, visible: true });
			return newElements;
		});
	}, []);

	const hide = useCallback((id: string) => {
		setElements((prev) => {
			const element = prev.get(id);
			if (!element) return prev;

			const newElements = new Map(prev);
			newElements.set(id, { ...element, visible: false });
			return newElements;
		});
	}, []);

	const toggle = useCallback((id: string) => {
		setElements((prev) => {
			const element = prev.get(id);
			if (!element) return prev;

			const newElements = new Map(prev);
			newElements.set(id, { ...element, visible: !element.visible });
			return newElements;
		});
	}, []);

	const update = useCallback((id: string, updates: Partial<SimpleFloatingElement>) => {
		setElements((prev) => {
			const element = prev.get(id);
			if (!element) return prev;

			const newElements = new Map(prev);
			newElements.set(id, { ...element, ...updates });
			return newElements;
		});
	}, []);

	const hideAll = useCallback(() => {
		setElements((prev) => {
			const newElements = new Map();
			prev.forEach((element, id) => {
				newElements.set(id, { ...element, visible: false });
			});
			return newElements;
		});
	}, []);

	const getVisibleElements = useCallback(() => {
		return Array.from(elements.values()).filter((el) => el.visible);
	}, [elements]);

	const contextValue: SimpleFloatingContextType = {
		elements,
		register,
		unregister,
		show,
		hide,
		toggle,
		update,
		hideAll,
		getVisibleElements,
	};

	return (
		<SimpleFloatingContext.Provider value={contextValue}>
			{children}
		</SimpleFloatingContext.Provider>
	);
}

export function useSimpleFloatingContext(): SimpleFloatingContextType {
	const context = useContext(SimpleFloatingContext);
	if (!context) {
		throw new Error('useSimpleFloatingContext must be used within a SimpleFloatingProvider');
	}
	return context;
}

// ============================================================================
// SIMPLE FLOATING UI MANAGER
// ============================================================================

export function SimpleFloatingManager() {
	const { elements } = useSimpleFloatingContext();

	const visibleElements = Array.from(elements.values()).filter((el) => el.visible);

	return (
		<div className="simple-floating-manager">
			{visibleElements.map((element) => (
				<div
					key={element.id}
					style={{
						position: 'fixed',
						top: element.position.top,
						bottom: element.position.bottom,
						left: element.position.left,
						right: element.position.right,
						zIndex: element.zIndex || 1000,
						pointerEvents: 'auto',
						...element.style,
					}}
					className={element.className}
					data-floating-id={element.id}
				>
					{element.content}
				</div>
			))}
		</div>
	);
}

// ============================================================================
// SIMPLE FLOATING UI HOOK
// ============================================================================

interface UseSimpleFloatingUIOptions {
	position: {
		top?: number;
		bottom?: number;
		left?: number;
		right?: number;
	};
	zIndex?: number;
	className?: string;
	style?: React.CSSProperties;
	autoShow?: boolean;
}

export function useSimpleFloatingUI(
	id: string,
	content: ReactNode,
	options: UseSimpleFloatingUIOptions
) {
	const { register, unregister, show, hide, toggle, elements } = useSimpleFloatingContext();

	// Register on mount
	React.useEffect(() => {
		register({
			id,
			content,
			position: options.position,
			zIndex: options.zIndex,
			className: options.className,
			style: options.style,
		});

		if (options.autoShow) {
			show(id);
		}

		return () => {
			unregister(id);
		};
	}, [id, register, unregister, show, options.autoShow]); // Minimal dependencies

	// Don't update content after registration to avoid infinite loops
	// Content should be stable or memoized by the caller

	const element = elements.get(id);
	const isVisible = element?.visible || false;

	return {
		show: () => show(id),
		hide: () => hide(id),
		toggle: () => toggle(id),
		isVisible,
		element,
	};
}
