import {
	BaseRepository,
	BaseRepositoryImpl,
	CollectionRepository,
	CollectionRepositoryImpl,
	CollectionStatsRepository,
	CollectionStatsRepositoryImpl,
	KeywordRepository,
	KeywordRepositoryImpl,
	LastSeenWordRepository,
	LastSeenWordRepositoryImpl,
	UserRepository,
	UserRepositoryImpl,
	WordRepository,
	WordRepositoryImpl,
} from '@/backend/repositories';
import {
	AuthService,
	AuthServiceImpl,
	CacheService,
	CollectionService,
	CollectionServiceImpl,
	CollectionStatsService,
	CollectionStatsServiceImpl,
	FeedbackService,
	FeedbackServiceImpl,
	KeywordService,
	KeywordServiceImpl,
	LLMService,
	LastSeenWordService,
	LastSeenWordServiceImpl,
	UserService,
	UserServiceImpl,
	WordService,
	WordServiceImpl,
} from '@/backend/services';
import { Feedback, PrismaClient } from '@prisma/client';

// MongoDB repositories
import { UserMongoRepositoryImpl } from '@/backend/repositories/user.mongo.repository';
import { WordMongoRepositoryImpl } from '@/backend/repositories/word.mongo.repository';
import { CollectionMongoRepositoryImpl } from '@/backend/repositories/collection.mongo.repository';
import {
	ParagraphMongoRepositoryImpl,
	KeywordMongoRepositoryImpl,
	LastSeenWordMongoRepositoryImpl,
	CollectionStatsMongoRepositoryImpl,
	FeedbackMongoRepositoryImpl,
} from '@/backend/repositories/other.mongo.repositories';

// Database abstraction
import {
	getDatabaseManager,
	isPrimaryDatabaseMongoDB,
} from '@/backend/database/database.abstraction';

// Database clients
let prismaClient: PrismaClient;
export const getPrismaClient = (): PrismaClient =>
	prismaClient || (prismaClient = new PrismaClient());

// Repository instances
let collectionRepository: CollectionRepository;
let keywordRepository: KeywordRepository;
let userRepository: UserRepository;
let wordRepository: WordRepository;
let lastSeenWordRepository: LastSeenWordRepository;
let feedbackRepository: BaseRepository<Feedback>;
let collectionStatsRepository: CollectionStatsRepository;

// Repository factory functions
export const getCollectionRepository = (): CollectionRepository => {
	if (!collectionRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			collectionRepository = new CollectionMongoRepositoryImpl();
		} else {
			collectionRepository = new CollectionRepositoryImpl(getPrismaClient());
		}
	}
	return collectionRepository;
};

export const getKeywordRepository = (): KeywordRepository => {
	if (!keywordRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			keywordRepository = new KeywordMongoRepositoryImpl();
		} else {
			keywordRepository = new KeywordRepositoryImpl(getPrismaClient());
		}
	}
	return keywordRepository;
};

export const getUserRepository = (): UserRepository => {
	if (!userRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			userRepository = new UserMongoRepositoryImpl();
		} else {
			userRepository = new UserRepositoryImpl(getPrismaClient());
		}
	}
	return userRepository;
};

export const getWordRepository = (): WordRepository => {
	if (!wordRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			wordRepository = new WordMongoRepositoryImpl();
		} else {
			wordRepository = new WordRepositoryImpl(getPrismaClient());
		}
	}
	return wordRepository;
};

export const getLastSeenWordRepository = (): LastSeenWordRepository => {
	if (!lastSeenWordRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			lastSeenWordRepository = new LastSeenWordMongoRepositoryImpl();
		} else {
			lastSeenWordRepository = new LastSeenWordRepositoryImpl(getPrismaClient());
		}
	}
	return lastSeenWordRepository;
};

export const getFeedbackRepository = (): BaseRepository<Feedback> => {
	if (!feedbackRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			feedbackRepository = new FeedbackMongoRepositoryImpl();
		} else {
			feedbackRepository = new BaseRepositoryImpl<Feedback>(getPrismaClient().feedback);
		}
	}
	return feedbackRepository;
};

export const getCollectionStatsRepository = (): CollectionStatsRepository => {
	if (!collectionStatsRepository) {
		if (isPrimaryDatabaseMongoDB()) {
			collectionStatsRepository = new CollectionStatsMongoRepositoryImpl();
		} else {
			collectionStatsRepository = new CollectionStatsRepositoryImpl(getPrismaClient());
		}
	}
	return collectionStatsRepository;
};

let userService: UserService | null = null;
export const getUserService = (): UserService =>
	userService || (userService = new UserServiceImpl(getUserRepository));

let authService: AuthService | null = null;
export const getAuthService = (): AuthService =>
	authService || (authService = new AuthServiceImpl(getUserService));

let cacheService: CacheService | null = null;
export const getCacheService = (): CacheService =>
	cacheService || (cacheService = new CacheService());

let feedbackService: FeedbackService | null = null;
export const getFeedbackService = (): FeedbackService =>
	feedbackService || (feedbackService = new FeedbackServiceImpl(getFeedbackRepository));

let lastSeenWordService: LastSeenWordService | null = null;
export const getLastSeenWordService = (): LastSeenWordService =>
	lastSeenWordService ||
	(lastSeenWordService = new LastSeenWordServiceImpl(getLastSeenWordRepository));

let llmService: LLMService | null = null;
export const getLLMService = (): LLMService =>
	llmService || (llmService = new LLMService(getWordService));

let wordService: WordService | null = null;
export const getWordService = (): WordService =>
	wordService ||
	(wordService = new WordServiceImpl(
		getWordRepository,
		getCollectionService,
		getLastSeenWordService
	));

let collectionService: CollectionService | null = null;
export const getCollectionService = (): CollectionService =>
	collectionService ||
	(collectionService = new CollectionServiceImpl(
		getCollectionRepository,
		getLLMService,
		getWordService
	));

let keywordService: any = null;
export const getKeywordService = (): KeywordService =>
	keywordService || (keywordService = new KeywordServiceImpl(getKeywordRepository));

let collectionStatsService: CollectionStatsService | null = null;
export const getCollectionStatsService = (): CollectionStatsService =>
	collectionStatsService ||
	(collectionStatsService = new CollectionStatsServiceImpl(getCollectionStatsRepository));

// Database management utilities
export const initializeDatabases = async (): Promise<void> => {
	const manager = getDatabaseManager();
	await manager.connectAll();
};

export const shutdownDatabases = async (): Promise<void> => {
	const manager = getDatabaseManager();
	await manager.disconnectAll();
};

export const getDatabaseHealthStatus = async (): Promise<
	Record<string, 'healthy' | 'unhealthy' | 'unknown'>
> => {
	const manager = getDatabaseManager();
	return await manager.healthCheck();
};

export const getDatabaseConnectionStatus = (): Record<string, boolean> => {
	const manager = getDatabaseManager();
	return manager.getConnectionStatus();
};

export const isPrimaryMongoDB = (): boolean => {
	return isPrimaryDatabaseMongoDB();
};

export const resetRepositories = (): void => {
	// Reset all repository instances to force re-initialization
	collectionRepository = null as any;
	keywordRepository = null as any;
	userRepository = null as any;
	wordRepository = null as any;
	lastSeenWordRepository = null as any;
	feedbackRepository = null as any;
	collectionStatsRepository = null as any;

	// Reset services that depend on repositories
	userService = null;
	authService = null;
	feedbackService = null;
	lastSeenWordService = null;
	wordService = null;
	collectionService = null;
	keywordService = null;
	collectionStatsService = null;
};
