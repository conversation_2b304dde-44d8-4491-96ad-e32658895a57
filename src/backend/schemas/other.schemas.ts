import { Schema, model, models, Types } from 'mongoose';
import { IKeyword, ILastSeenWord, IFeedback, ICollectionStats } from './types';

// Keyword Schema
const KeywordSchema = new Schema<IKeyword>(
	{
		content: {
			type: String,
			required: true,
			trim: true,
		},
		user_id: {
			type: Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'keywords',
	}
);

// Indexes for Keyword
KeywordSchema.index({ user_id: 1 });
KeywordSchema.index({ content: 'text' }); // Text search index
KeywordSchema.index({ user_id: 1, content: 1 });

// Virtual for id compatibility with Prisma
KeywordSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
KeywordSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		ret.user_id = ret.user_id.toString();
		delete ret._id;
		delete ret.__v;
		return ret;
	},
});

// Static methods for Keyword
KeywordSchema.statics.findUserKeywords = function (userId: string) {
	return this.find({ user_id: new Types.ObjectId(userId) });
};

KeywordSchema.statics.search = function (query: string, userId?: string) {
	const searchQuery: any = {
		$text: { $search: query },
	};
	
	if (userId) {
		searchQuery.user_id = new Types.ObjectId(userId);
	}
	
	return this.find(searchQuery);
};

// LastSeenWord Schema
const LastSeenWordSchema = new Schema<ILastSeenWord>(
	{
		user_id: {
			type: Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		word_id: {
			type: Schema.Types.ObjectId,
			ref: 'Word',
			required: true,
		},
		last_seen_at: {
			type: Date,
			required: true,
			default: Date.now,
		},
		review_count: {
			type: Number,
			default: 1,
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'last_seen_words',
	}
);

// Indexes for LastSeenWord
LastSeenWordSchema.index({ user_id: 1, word_id: 1 }, { unique: true });
LastSeenWordSchema.index({ user_id: 1 });
LastSeenWordSchema.index({ word_id: 1 });
LastSeenWordSchema.index({ last_seen_at: 1 });

// Virtual for id compatibility with Prisma
LastSeenWordSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
LastSeenWordSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		ret.user_id = ret.user_id.toString();
		ret.word_id = ret.word_id.toString();
		delete ret._id;
		delete ret.__v;
		return ret;
	},
});

// Static methods for LastSeenWord
LastSeenWordSchema.statics.findLastSeenWord = function (userId: string, wordId: string) {
	return this.findOne({ 
		user_id: new Types.ObjectId(userId), 
		word_id: new Types.ObjectId(wordId) 
	});
};

LastSeenWordSchema.statics.findLastSeenWordsByWordIds = function (userId: string, wordIds: string[]) {
	const objectIds = wordIds.map(id => new Types.ObjectId(id));
	return this.find({ 
		user_id: new Types.ObjectId(userId), 
		word_id: { $in: objectIds } 
	});
};

LastSeenWordSchema.statics.updateOrCreateLastSeenWord = async function (userId: string, wordId: string) {
	return this.findOneAndUpdate(
		{ 
			user_id: new Types.ObjectId(userId), 
			word_id: new Types.ObjectId(wordId) 
		},
		{
			$set: { last_seen_at: new Date() },
			$inc: { review_count: 1 },
			$setOnInsert: { 
				user_id: new Types.ObjectId(userId), 
				word_id: new Types.ObjectId(wordId) 
			}
		},
		{ 
			upsert: true, 
			new: true 
		}
	);
};

// Feedback Schema
const FeedbackSchema = new Schema<IFeedback>(
	{
		message: {
			type: String,
			required: true,
		},
		user_id: {
			type: Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		status: {
			type: String,
			default: 'pending',
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'feedbacks',
	}
);

// Indexes for Feedback
FeedbackSchema.index({ user_id: 1 });
FeedbackSchema.index({ status: 1 });
FeedbackSchema.index({ created_at: -1 });

// Virtual for id compatibility with Prisma
FeedbackSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
FeedbackSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		ret.user_id = ret.user_id.toString();
		delete ret._id;
		delete ret.__v;
		return ret;
	},
});

// CollectionStats Schema
const CollectionStatsSchema = new Schema<ICollectionStats>(
	{
		collection_id: {
			type: Schema.Types.ObjectId,
			ref: 'Collection',
			required: true,
		},
		user_id: {
			type: Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		date: {
			type: Date,
			required: true,
		},
		words_reviewed_count: {
			type: Number,
			default: 0,
		},
		qa_practice_submissions: {
			type: Number,
			default: 0,
		},
		paragraph_practice_submissions: {
			type: Number,
			default: 0,
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'collection_stats',
	}
);

// Indexes for CollectionStats
CollectionStatsSchema.index({ collection_id: 1, user_id: 1, date: 1 }, { unique: true });
CollectionStatsSchema.index({ collection_id: 1 });
CollectionStatsSchema.index({ user_id: 1 });
CollectionStatsSchema.index({ date: 1 });

// Virtual for id compatibility with Prisma
CollectionStatsSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
CollectionStatsSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		ret.collection_id = ret.collection_id.toString();
		ret.user_id = ret.user_id.toString();
		delete ret._id;
		delete ret.__v;
		return ret;
	},
});

// Export the models
export const KeywordModel = models.Keyword || model<IKeyword>('Keyword', KeywordSchema);
export const LastSeenWordModel = models.LastSeenWord || model<ILastSeenWord>('LastSeenWord', LastSeenWordSchema);
export const FeedbackModel = models.Feedback || model<IFeedback>('Feedback', FeedbackSchema);
export const CollectionStatsModel = models.CollectionStats || model<ICollectionStats>('CollectionStats', CollectionStatsSchema);

// Export the schemas for testing
export { KeywordSchema, LastSeenWordSchema, FeedbackSchema, CollectionStatsSchema };
