import { Schema, model, models, Types } from 'mongoose';
import { ICollection, Language } from './types';

const CollectionSchema = new Schema<ICollection>(
	{
		name: {
			type: String,
			required: true,
			trim: true,
		},
		target_language: {
			type: String,
			enum: Object.values(Language),
			default: Language.EN,
		},
		source_language: {
			type: String,
			enum: Object.values(Language),
			default: Language.VI,
		},
		user_id: {
			type: Schema.Types.ObjectId,
			ref: 'User',
			required: true,
		},
		word_ids: {
			type: [Schema.Types.ObjectId],
			ref: 'Word',
			default: [],
		},
		paragraph_ids: {
			type: [Schema.Types.ObjectId],
			ref: 'Paragraph',
			default: [],
		},
		keyword_ids: {
			type: [Schema.Types.ObjectId],
			ref: 'Keyword',
			default: [],
		},
		enable_learn_word_notification: {
			type: Boolean,
			default: false,
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'collections',
	}
);

// Indexes
CollectionSchema.index({ user_id: 1 });
CollectionSchema.index({ user_id: 1, name: 1 });
CollectionSchema.index({ target_language: 1 });
CollectionSchema.index({ source_language: 1 });

// Virtual for id compatibility with Prisma
CollectionSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
CollectionSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		delete ret._id;
		delete ret.__v;
		
		// Convert ObjectIds to strings for compatibility
		ret.user_id = ret.user_id.toString();
		ret.word_ids = ret.word_ids.map((id: Types.ObjectId) => id.toString());
		ret.paragraph_ids = ret.paragraph_ids.map((id: Types.ObjectId) => id.toString());
		ret.keyword_ids = ret.keyword_ids.map((id: Types.ObjectId) => id.toString());
		
		return ret;
	},
});

// Pre-save middleware for validation
CollectionSchema.pre('save', function (next) {
	// Ensure name is not empty
	if (!this.name || this.name.trim().length === 0) {
		return next(new Error('Collection name cannot be empty'));
	}
	
	// Validate language combination
	if (this.target_language === this.source_language) {
		return next(new Error('Target language and source language cannot be the same'));
	}
	
	next();
});

// Static methods
CollectionSchema.statics.findUserCollections = function (userId: string) {
	return this.find({ user_id: new Types.ObjectId(userId) });
};

CollectionSchema.statics.findByUserIdAndName = function (userId: string, name: string) {
	return this.findOne({ 
		user_id: new Types.ObjectId(userId), 
		name 
	});
};

CollectionSchema.statics.findCollectionsWithWords = function (wordIds: string[]) {
	const objectIds = wordIds.map(id => new Types.ObjectId(id));
	return this.find({ 
		word_ids: { $in: objectIds } 
	});
};

CollectionSchema.statics.findCollectionsWithParagraphs = function (paragraphIds: string[]) {
	const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
	return this.find({ 
		paragraph_ids: { $in: objectIds } 
	});
};

CollectionSchema.statics.findCollectionsWithKeywords = function (keywordIds: string[]) {
	const objectIds = keywordIds.map(id => new Types.ObjectId(id));
	return this.find({ 
		keyword_ids: { $in: objectIds } 
	});
};

// Instance methods
CollectionSchema.methods.addWords = function (wordIds: string[]) {
	const objectIds = wordIds.map(id => new Types.ObjectId(id));
	this.word_ids.push(...objectIds);
	// Remove duplicates
	this.word_ids = [...new Set(this.word_ids.map(id => id.toString()))].map(id => new Types.ObjectId(id));
	return this.save();
};

CollectionSchema.methods.removeWords = function (wordIds: string[]) {
	const objectIds = wordIds.map(id => new Types.ObjectId(id));
	this.word_ids = this.word_ids.filter(id => 
		!objectIds.some(removeId => removeId.equals(id))
	);
	return this.save();
};

CollectionSchema.methods.addParagraphs = function (paragraphIds: string[]) {
	const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
	this.paragraph_ids.push(...objectIds);
	// Remove duplicates
	this.paragraph_ids = [...new Set(this.paragraph_ids.map(id => id.toString()))].map(id => new Types.ObjectId(id));
	return this.save();
};

CollectionSchema.methods.removeParagraphs = function (paragraphIds: string[]) {
	const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
	this.paragraph_ids = this.paragraph_ids.filter(id => 
		!objectIds.some(removeId => removeId.equals(id))
	);
	return this.save();
};

CollectionSchema.methods.addKeywords = function (keywordIds: string[]) {
	const objectIds = keywordIds.map(id => new Types.ObjectId(id));
	this.keyword_ids.push(...objectIds);
	// Remove duplicates
	this.keyword_ids = [...new Set(this.keyword_ids.map(id => id.toString()))].map(id => new Types.ObjectId(id));
	return this.save();
};

CollectionSchema.methods.removeKeywords = function (keywordIds: string[]) {
	const objectIds = keywordIds.map(id => new Types.ObjectId(id));
	this.keyword_ids = this.keyword_ids.filter(id => 
		!objectIds.some(removeId => removeId.equals(id))
	);
	return this.save();
};

CollectionSchema.methods.getWordCount = function () {
	return this.word_ids.length;
};

CollectionSchema.methods.getParagraphCount = function () {
	return this.paragraph_ids.length;
};

CollectionSchema.methods.getKeywordCount = function () {
	return this.keyword_ids.length;
};

// Export the model
export const CollectionModel = models.Collection || model<ICollection>('Collection', CollectionSchema);

// Export the schema for testing
export { CollectionSchema };
