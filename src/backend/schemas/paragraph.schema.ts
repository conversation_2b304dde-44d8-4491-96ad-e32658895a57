import { Schema, model, models } from 'mongoose';
import { IParagraph, IMultipleChoiceExercise, Language, Difficulty, Length } from './types';

// MultipleChoiceExercise sub-schema
const MultipleChoiceExerciseSchema = new Schema<IMultipleChoiceExercise>(
	{
		question: {
			type: String,
			required: true,
		},
		options: {
			type: [String],
			required: true,
			validate: {
				validator: function (v: string[]) {
					return v && v.length >= 2;
				},
				message: 'At least 2 options are required',
			},
		},
		answer: {
			type: Number,
			required: true,
			validate: {
				validator: function (v: number) {
					return v >= 0 && v < (this as any).options.length;
				},
				message: 'Answer index must be valid for the options array',
			},
		},
		explanation: {
			type: String,
		},
	},
	{
		_id: true, // Ensure each exercise has its own _id
	}
);

// Paragraph schema
const ParagraphSchema = new Schema<IParagraph>(
	{
		title: {
			type: String,
			required: true,
			trim: true,
		},
		content: {
			type: String,
			required: true,
		},
		language: {
			type: String,
			enum: Object.values(Language),
			required: true,
		},
		difficulty: {
			type: String,
			enum: Object.values(Difficulty),
			required: true,
		},
		length: {
			type: String,
			enum: Object.values(Length),
			required: true,
		},
		multiple_choice_exercises: {
			type: [MultipleChoiceExerciseSchema],
			default: [],
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'paragraphs',
	}
);

// Indexes
ParagraphSchema.index({ language: 1 });
ParagraphSchema.index({ difficulty: 1 });
ParagraphSchema.index({ length: 1 });
ParagraphSchema.index({ language: 1, difficulty: 1 });
ParagraphSchema.index({ title: 'text', content: 'text' }); // Text search index

// Virtual for id compatibility with Prisma
ParagraphSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
ParagraphSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		delete ret._id;
		delete ret.__v;
		
		// Transform multiple choice exercises to include id field
		if (ret.multiple_choice_exercises) {
			ret.multiple_choice_exercises = ret.multiple_choice_exercises.map((exercise: any) => {
				exercise.id = exercise._id.toString();
				delete exercise._id;
				return exercise;
			});
		}
		
		return ret;
	},
});

// Pre-save middleware for validation
ParagraphSchema.pre('save', function (next) {
	// Ensure title and content are not empty
	if (!this.title || this.title.trim().length === 0) {
		return next(new Error('Paragraph title cannot be empty'));
	}
	
	if (!this.content || this.content.trim().length === 0) {
		return next(new Error('Paragraph content cannot be empty'));
	}
	
	// Validate multiple choice exercises
	if (this.multiple_choice_exercises && this.multiple_choice_exercises.length > 0) {
		for (const exercise of this.multiple_choice_exercises) {
			if (exercise.answer < 0 || exercise.answer >= exercise.options.length) {
				return next(new Error('Answer index must be valid for the options array'));
			}
		}
	}
	
	next();
});

// Static methods
ParagraphSchema.statics.findByLanguage = function (language: Language, limit?: number) {
	const query = this.find({ language });
	if (limit) {
		query.limit(limit);
	}
	return query;
};

ParagraphSchema.statics.findByDifficulty = function (difficulty: Difficulty, limit?: number) {
	const query = this.find({ difficulty });
	if (limit) {
		query.limit(limit);
	}
	return query;
};

ParagraphSchema.statics.findByIds = function (ids: string[]) {
	return this.find({ _id: { $in: ids } });
};

ParagraphSchema.statics.createMany = async function (data: any[]) {
	return this.insertMany(data);
};

ParagraphSchema.statics.findWithExercises = function (id: string) {
	return this.findById(id);
};

ParagraphSchema.statics.searchParagraphs = function (searchTerm: string, language?: Language, limit: number = 10) {
	const query: any = {
		$text: { $search: searchTerm },
	};
	
	if (language) {
		query.language = language;
	}
	
	return this.find(query).limit(limit);
};

// Instance methods
ParagraphSchema.methods.addExercise = function (exercise: Partial<IMultipleChoiceExercise>) {
	this.multiple_choice_exercises.push(exercise);
	return this.save();
};

ParagraphSchema.methods.updateExercise = function (exerciseId: string, update: Partial<IMultipleChoiceExercise>) {
	const exercise = this.multiple_choice_exercises.id(exerciseId);
	if (exercise) {
		Object.assign(exercise, update);
		return this.save();
	}
	throw new Error('Exercise not found');
};

ParagraphSchema.methods.removeExercise = function (exerciseId: string) {
	this.multiple_choice_exercises.pull({ _id: exerciseId });
	return this.save();
};

ParagraphSchema.methods.getExerciseCount = function () {
	return this.multiple_choice_exercises.length;
};

ParagraphSchema.methods.getWordCount = function () {
	// Simple word count based on spaces
	return this.content.split(/\s+/).filter(word => word.length > 0).length;
};

// Export the model
export const ParagraphModel = models.Paragraph || model<IParagraph>('Paragraph', ParagraphSchema);

// Export the schema for testing
export { ParagraphSchema, MultipleChoiceExerciseSchema };
