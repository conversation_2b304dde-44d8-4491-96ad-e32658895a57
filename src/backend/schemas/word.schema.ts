import { Schema, model, models, Types } from 'mongoose';
import { IWord, IDefinition, IExplain, IExample, Language, PartsOfSpeech } from './types';

// Explain sub-schema
const ExplainSchema = new Schema<IExplain>(
	{
		EN: {
			type: String,
			required: true,
		},
		VI: {
			type: String,
			required: true,
		},
	},
	{
		_id: true, // Ensure each explain has its own _id
	}
);

// Example sub-schema
const ExampleSchema = new Schema<IExample>(
	{
		EN: {
			type: String,
			required: true,
		},
		VI: {
			type: String,
			required: true,
		},
	},
	{
		_id: true, // Ensure each example has its own _id
	}
);

// Definition sub-schema
const DefinitionSchema = new Schema<IDefinition>(
	{
		pos: {
			type: [String],
			enum: Object.values(PartsOfSpeech),
			required: true,
			validate: {
				validator: function (v: string[]) {
					return v && v.length > 0;
				},
				message: 'At least one part of speech is required',
			},
		},
		ipa: {
			type: String,
			required: true,
		},
		images: {
			type: [String],
			default: [],
		},
		explains: {
			type: [ExplainSchema],
			default: [],
		},
		examples: {
			type: [ExampleSchema],
			default: [],
		},
	},
	{
		_id: true, // Ensure each definition has its own _id
	}
);

// Word schema
const WordSchema = new Schema<IWord>(
	{
		term: {
			type: String,
			required: true,
			trim: true,
		},
		language: {
			type: String,
			enum: Object.values(Language),
			required: true,
		},
		audio_url: {
			type: String,
		},
		definitions: {
			type: [DefinitionSchema],
			default: [],
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'words',
	}
);

// Indexes
WordSchema.index({ term: 1, language: 1 }, { unique: true });
WordSchema.index({ term: 'text' }); // Text search index
WordSchema.index({ language: 1 });

// Virtual for id compatibility with Prisma
WordSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
WordSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		delete ret._id;
		delete ret.__v;
		
		// Transform definitions to include id field
		if (ret.definitions) {
			ret.definitions = ret.definitions.map((def: any) => {
				def.id = def._id.toString();
				delete def._id;
				
				// Transform explains
				if (def.explains) {
					def.explains = def.explains.map((explain: any) => {
						explain.id = explain._id.toString();
						delete explain._id;
						return explain;
					});
				}
				
				// Transform examples
				if (def.examples) {
					def.examples = def.examples.map((example: any) => {
						example.id = example._id.toString();
						delete example._id;
						return example;
					});
				}
				
				return def;
			});
		}
		
		return ret;
	},
});

// Pre-save middleware for validation
WordSchema.pre('save', function (next) {
	// Ensure term is not empty
	if (!this.term || this.term.trim().length === 0) {
		return next(new Error('Term cannot be empty'));
	}
	
	// Validate definitions
	if (this.definitions && this.definitions.length > 0) {
		for (const definition of this.definitions) {
			if (!definition.pos || definition.pos.length === 0) {
				return next(new Error('Each definition must have at least one part of speech'));
			}
		}
	}
	
	next();
});

// Static methods
WordSchema.statics.findByTerm = function (term: string, language: Language) {
	return this.findOne({ term, language });
};

WordSchema.statics.searchWords = function (term: string, language?: Language, limit: number = 10) {
	const query: any = {
		$text: { $search: term },
	};
	
	if (language) {
		query.language = language;
	}
	
	return this.find(query).limit(limit);
};

WordSchema.statics.findWordsByIds = function (wordIds: string[]) {
	const objectIds = wordIds.map(id => new Types.ObjectId(id));
	return this.find({ _id: { $in: objectIds } });
};

WordSchema.statics.findOrCreateWords = async function (terms: string[], language: Language) {
	const words = [];
	
	for (const term of terms) {
		let word = await this.findByTerm(term, language);
		
		if (!word) {
			word = new this({
				term,
				language,
				definitions: [],
			});
			await word.save();
		}
		
		words.push(word);
	}
	
	return words;
};

// Instance methods
WordSchema.methods.addDefinition = function (definition: Partial<IDefinition>) {
	this.definitions.push(definition);
	return this.save();
};

WordSchema.methods.updateDefinition = function (definitionId: string, update: Partial<IDefinition>) {
	const definition = this.definitions.id(definitionId);
	if (definition) {
		Object.assign(definition, update);
		return this.save();
	}
	throw new Error('Definition not found');
};

WordSchema.methods.removeDefinition = function (definitionId: string) {
	this.definitions.pull({ _id: definitionId });
	return this.save();
};

// Export the model
export const WordModel = models.Word || model<IWord>('Word', WordSchema);

// Export the schema for testing
export { WordSchema, DefinitionSchema, ExplainSchema, ExampleSchema };
