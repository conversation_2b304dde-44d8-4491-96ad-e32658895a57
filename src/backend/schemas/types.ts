import { Document, Types } from 'mongoose';

// Enums matching Prisma schema
export enum Language {
	EN = 'EN',
	VI = 'VI',
}

export enum Provider {
	TELEGRAM = 'TELEGRAM',
	USERNAME_PASSWORD = 'USERNAME_PASSWORD',
	GOOGLE = 'GOOGLE',
}

export enum PartsOfSpeech {
	NOUN = 'NOUN',
	VERB = 'VERB',
	ADJECTIVE = 'ADJECTIVE',
	ADVERB = 'ADVERB',
	PRONOUN = 'PRONOUN',
	PREPOSITION = 'PREPOSITION',
	CONJUNCTION = 'CONJUNCTION',
	INTERJECTION = 'INTERJECTION',
}

export enum Difficulty {
	BEGINNER = 'BEGINNER',
	INTERMEDIATE = 'INTERMEDIATE',
	ADVANCED = 'ADVANCED',
}

export enum Length {
	SHORT = 'SHORT',
	MEDIUM = 'MEDIUM',
	LONG = 'LONG',
}

// Base document interface
export interface BaseDocument extends Document {
	_id: Types.ObjectId;
	created_at: Date;
	updated_at: Date;
}

// User document interface
export interface IUser extends BaseDocument {
	provider: Provider;
	provider_id: string;
	username?: string;
	password_hash?: string;
	disabled: boolean;
}

// Embedded document interfaces for Word
export interface IExplain {
	_id: Types.ObjectId;
	EN: string;
	VI: string;
}

export interface IExample {
	_id: Types.ObjectId;
	EN: string;
	VI: string;
}

export interface IDefinition {
	_id: Types.ObjectId;
	pos: PartsOfSpeech[];
	ipa: string;
	images: string[];
	explains: IExplain[];
	examples: IExample[];
}

// Word document interface
export interface IWord extends BaseDocument {
	term: string;
	language: Language;
	audio_url?: string;
	definitions: IDefinition[];
}

// Collection document interface
export interface ICollection extends BaseDocument {
	name: string;
	target_language: Language;
	source_language: Language;
	user_id: Types.ObjectId;
	word_ids: Types.ObjectId[];
	paragraph_ids: Types.ObjectId[];
	keyword_ids: Types.ObjectId[];
	enable_learn_word_notification: boolean;
}

// Keyword document interface
export interface IKeyword extends BaseDocument {
	content: string;
	user_id: Types.ObjectId;
}

// LastSeenWord document interface
export interface ILastSeenWord extends BaseDocument {
	user_id: Types.ObjectId;
	word_id: Types.ObjectId;
	last_seen_at: Date;
	review_count: number;
}

// Embedded document interface for Paragraph
export interface IMultipleChoiceExercise {
	_id: Types.ObjectId;
	question: string;
	options: string[];
	answer: number;
	explanation?: string;
}

// Paragraph document interface
export interface IParagraph extends BaseDocument {
	title: string;
	content: string;
	language: Language;
	difficulty: Difficulty;
	length: Length;
	multiple_choice_exercises: IMultipleChoiceExercise[];
}

// Feedback document interface
export interface IFeedback extends BaseDocument {
	message: string;
	user_id: Types.ObjectId;
	status: string;
}

// CollectionStats document interface
export interface ICollectionStats extends BaseDocument {
	collection_id: Types.ObjectId;
	user_id: Types.ObjectId;
	date: Date;
	words_reviewed_count: number;
	qa_practice_submissions: number;
	paragraph_practice_submissions: number;
}

// Type guards for runtime type checking
export function isValidLanguage(value: string): value is Language {
	return Object.values(Language).includes(value as Language);
}

export function isValidProvider(value: string): value is Provider {
	return Object.values(Provider).includes(value as Provider);
}

export function isValidPartsOfSpeech(value: string): value is PartsOfSpeech {
	return Object.values(PartsOfSpeech).includes(value as PartsOfSpeech);
}

export function isValidDifficulty(value: string): value is Difficulty {
	return Object.values(Difficulty).includes(value as Difficulty);
}

export function isValidLength(value: string): value is Length {
	return Object.values(Length).includes(value as Length);
}

// Utility types for creating documents
export type CreateUserInput = Omit<IUser, '_id' | 'created_at' | 'updated_at' | 'disabled'> & {
	disabled?: boolean;
};

export type CreateWordInput = Omit<IWord, '_id' | 'created_at' | 'updated_at'>;

export type CreateCollectionInput = Omit<ICollection, '_id' | 'created_at' | 'updated_at'>;

export type CreateKeywordInput = Omit<IKeyword, '_id' | 'created_at' | 'updated_at'>;

export type CreateLastSeenWordInput = Omit<ILastSeenWord, '_id' | 'created_at' | 'updated_at' | 'review_count'> & {
	review_count?: number;
};

export type CreateParagraphInput = Omit<IParagraph, '_id' | 'created_at' | 'updated_at'>;

export type CreateFeedbackInput = Omit<IFeedback, '_id' | 'created_at' | 'updated_at' | 'status'> & {
	status?: string;
};

export type CreateCollectionStatsInput = Omit<ICollectionStats, '_id' | 'created_at' | 'updated_at'>;

// Update types
export type UpdateUserInput = Partial<Omit<IUser, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateWordInput = Partial<Omit<IWord, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateCollectionInput = Partial<Omit<ICollection, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateKeywordInput = Partial<Omit<IKeyword, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateLastSeenWordInput = Partial<Omit<ILastSeenWord, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateParagraphInput = Partial<Omit<IParagraph, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateFeedbackInput = Partial<Omit<IFeedback, '_id' | 'created_at' | 'updated_at'>>;
export type UpdateCollectionStatsInput = Partial<Omit<ICollectionStats, '_id' | 'created_at' | 'updated_at'>>;
