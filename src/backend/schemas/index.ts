// Export all types
export * from './types';

// Export all schemas and models
export * from './user.schema';
export * from './word.schema';
export * from './collection.schema';
export * from './paragraph.schema';
export * from './other.schemas';

// Re-export models for convenience
export { UserModel } from './user.schema';
export { WordModel } from './word.schema';
export { CollectionModel } from './collection.schema';
export { ParagraphModel } from './paragraph.schema';
export { 
	KeywordModel, 
	LastSeenWordModel, 
	FeedbackModel, 
	CollectionStatsModel 
} from './other.schemas';

// Export connection utilities
export { connectToMongoDB, disconnectFromMongoDB, getMongoDBConnectionStatus, checkMongoDBHealth } from '@/config/mongodb';
