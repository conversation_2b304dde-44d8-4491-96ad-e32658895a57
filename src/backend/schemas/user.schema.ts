import { Schema, model, models } from 'mongoose';
import { IUser, Provider } from './types';

const UserSchema = new Schema<IUser>(
	{
		provider: {
			type: String,
			enum: Object.values(Provider),
			required: true,
		},
		provider_id: {
			type: String,
			required: true,
		},
		username: {
			type: String,
			sparse: true, // Allows multiple null values
		},
		password_hash: {
			type: String,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
	},
	{
		timestamps: {
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		},
		collection: 'users',
	}
);

// Indexes
UserSchema.index({ provider: 1, provider_id: 1 }, { unique: true });
UserSchema.index({ username: 1 }, { unique: true, sparse: true });

// Virtual for id compatibility with Prisma
UserSchema.virtual('id').get(function () {
	return this._id.toHexString();
});

// Ensure virtual fields are serialized
UserSchema.set('toJSON', {
	virtuals: true,
	transform: function (doc, ret) {
		ret.id = ret._id.toString();
		delete ret._id;
		delete ret.__v;
		return ret;
	},
});

// Pre-save middleware for validation
UserSchema.pre('save', function (next) {
	// Validate provider-specific requirements
	if (this.provider === Provider.USERNAME_PASSWORD && !this.username) {
		return next(new Error('Username is required for USERNAME_PASSWORD provider'));
	}
	
	if (this.provider === Provider.USERNAME_PASSWORD && !this.password_hash) {
		return next(new Error('Password hash is required for USERNAME_PASSWORD provider'));
	}
	
	next();
});

// Static methods
UserSchema.statics.findByProviderId = function (provider: Provider, providerId: string) {
	return this.findOne({ provider, provider_id: providerId });
};

UserSchema.statics.findByUsername = function (username: string) {
	return this.findOne({ username });
};

UserSchema.statics.searchUsers = function (searchTerm: string, limit: number = 10) {
	return this.find({
		$or: [
			{ provider_id: { $regex: searchTerm, $options: 'i' } },
			{ username: { $regex: searchTerm, $options: 'i' } },
		],
	}).limit(limit);
};

// Instance methods
UserSchema.methods.toSafeObject = function () {
	const obj = this.toObject();
	delete obj.password_hash;
	return obj;
};

// Export the model
export const UserModel = models.User || model<IUser>('User', UserSchema);

// Export the schema for testing
export { UserSchema };
