import { FilterQuery } from 'mongoose';
import { BaseMongoRepositoryImpl, MongoBaseDocument } from './base.mongo.repository';
import { UserRepository } from './user.repository';
import { UserModel, IUser, Provider } from '@/backend/schemas';

// Extend IUser to include MongoBaseDocument properties
interface UserDocument extends IUser, MongoBaseDocument {}

export class UserMongoRepositoryImpl extends BaseMongoRepositoryImpl<UserDocument> implements UserRepository {
	constructor() {
		super(UserModel as any);
	}

	async findByProviderId(provider: Provider, providerId: string): Promise<UserDocument | null> {
		try {
			return await this.model.findOne({
				provider,
				provider_id: providerId,
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find user by provider ID: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByUsername(username: string): Promise<UserDocument | null> {
		try {
			return await this.model.findOne({
				username,
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find user by username: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async searchUsers(searchTerm: string, limit: number = 10): Promise<UserDocument[]> {
		try {
			return await this.model.find({
				$or: [
					{ provider_id: { $regex: searchTerm, $options: 'i' } },
					{ username: { $regex: searchTerm, $options: 'i' } },
				],
			}).limit(limit).exec();
		} catch (error) {
			throw new Error(`Failed to search users: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Override base methods to ensure proper typing
	override async findById(id: string): Promise<UserDocument | null> {
		return super.findById(id) as Promise<UserDocument | null>;
	}

	override async findOne(query: Record<string, unknown>): Promise<UserDocument | null> {
		return super.findOne(query) as Promise<UserDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<UserDocument[]> {
		return super.find(query) as Promise<UserDocument[]>;
	}

	override async create(data: Record<string, unknown>): Promise<UserDocument> {
		// Validate required fields for different providers
		const userData = data as Partial<IUser>;
		
		if (userData.provider === Provider.USERNAME_PASSWORD) {
			if (!userData.username) {
				throw new Error('Username is required for USERNAME_PASSWORD provider');
			}
			if (!userData.password_hash) {
				throw new Error('Password hash is required for USERNAME_PASSWORD provider');
			}
		}

		if (!userData.provider_id) {
			throw new Error('Provider ID is required');
		}

		return super.create(data) as Promise<UserDocument>;
	}

	override async update(id: string, data: Record<string, unknown>): Promise<UserDocument> {
		return super.update(id, data) as Promise<UserDocument>;
	}

	// Additional MongoDB-specific methods
	async findByProviderAndId(provider: Provider, providerId: string): Promise<UserDocument | null> {
		return this.findByProviderId(provider, providerId);
	}

	async findActiveUsers(limit?: number): Promise<UserDocument[]> {
		const query = this.model.find({ disabled: { $ne: true } });
		if (limit) {
			query.limit(limit);
		}
		return query.exec();
	}

	async findDisabledUsers(limit?: number): Promise<UserDocument[]> {
		const query = this.model.find({ disabled: true });
		if (limit) {
			query.limit(limit);
		}
		return query.exec();
	}

	async countUsersByProvider(): Promise<Record<Provider, number>> {
		try {
			const pipeline = [
				{
					$group: {
						_id: '$provider',
						count: { $sum: 1 },
					},
				},
			];

			const results = await this.model.aggregate(pipeline).exec();
			
			// Initialize counts for all providers
			const counts: Record<Provider, number> = {
				[Provider.TELEGRAM]: 0,
				[Provider.USERNAME_PASSWORD]: 0,
				[Provider.GOOGLE]: 0,
			};

			// Fill in actual counts
			for (const result of results) {
				if (result._id in counts) {
					counts[result._id as Provider] = result.count;
				}
			}

			return counts;
		} catch (error) {
			throw new Error(`Failed to count users by provider: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findRecentUsers(days: number = 7, limit: number = 10): Promise<UserDocument[]> {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - days);

			return await this.model.find({
				created_at: { $gte: cutoffDate },
			})
			.sort({ created_at: -1 })
			.limit(limit)
			.exec();
		} catch (error) {
			throw new Error(`Failed to find recent users: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async updateLastActivity(userId: string): Promise<UserDocument> {
		try {
			const updated = await this.model.findByIdAndUpdate(
				userId,
				{ updated_at: new Date() },
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('User not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to update last activity: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async disableUser(userId: string): Promise<UserDocument> {
		try {
			const updated = await this.model.findByIdAndUpdate(
				userId,
				{ disabled: true },
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('User not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to disable user: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async enableUser(userId: string): Promise<UserDocument> {
		try {
			const updated = await this.model.findByIdAndUpdate(
				userId,
				{ disabled: false },
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('User not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to enable user: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Utility method to check if username is available
	async isUsernameAvailable(username: string, excludeUserId?: string): Promise<boolean> {
		try {
			const filter: FilterQuery<UserDocument> = { username };
			
			if (excludeUserId) {
				filter._id = { $ne: excludeUserId };
			}

			const existingUser = await this.model.findOne(filter).exec();
			return !existingUser;
		} catch (error) {
			throw new Error(`Failed to check username availability: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Utility method to check if provider ID is available
	async isProviderIdAvailable(provider: Provider, providerId: string, excludeUserId?: string): Promise<boolean> {
		try {
			const filter: FilterQuery<UserDocument> = { provider, provider_id: providerId };
			
			if (excludeUserId) {
				filter._id = { $ne: excludeUserId };
			}

			const existingUser = await this.model.findOne(filter).exec();
			return !existingUser;
		} catch (error) {
			throw new Error(`Failed to check provider ID availability: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}
}
