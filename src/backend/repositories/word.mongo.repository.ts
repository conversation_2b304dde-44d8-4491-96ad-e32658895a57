import { Types } from 'mongoose';
import { BaseMongoRepositoryImpl, MongoBaseDocument } from './base.mongo.repository';
import { WordRepository } from './word.repository';
import { WordModel, IWord, Language } from '@/backend/schemas';

// Extend IWord to include MongoBaseDocument properties
interface WordDocument extends IWord, MongoBaseDocument {}

export class WordMongoRepositoryImpl extends BaseMongoRepositoryImpl<WordDocument> implements WordRepository {
	constructor() {
		super(WordModel as any);
	}

	// Override base methods to ensure proper typing
	override async findById(id: string): Promise<WordDocument | null> {
		return super.findById(id) as Promise<WordDocument | null>;
	}

	override async findOne(query: Record<string, unknown>): Promise<WordDocument | null> {
		return super.findOne(query) as Promise<WordDocument | null>;
	}

	override async find(query: Record<string, unknown>, limit?: number): Promise<WordDocument[]> {
		try {
			const mongoQuery = this.model.find(query);
			if (limit) {
				mongoQuery.limit(limit);
			}
			return await mongoQuery.exec();
		} catch (error) {
			throw new Error(`Failed to find words: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	override async create(data: Record<string, unknown>): Promise<WordDocument> {
		const wordData = data as Partial<IWord>;
		
		if (!wordData.term || !wordData.language) {
			throw new Error('Term and language are required');
		}

		return super.create(data) as Promise<WordDocument>;
	}

	override async update(id: string, data: Record<string, unknown>): Promise<WordDocument> {
		return super.update(id, data) as Promise<WordDocument>;
	}

	// WordRepository interface methods
	async searchWords(term: string, language?: Language, limit: number = 10): Promise<WordDocument[]> {
		try {
			const query: any = {
				$text: { $search: term },
			};
			
			if (language) {
				query.language = language;
			}
			
			return await this.model.find(query)
				.limit(limit)
				.sort({ score: { $meta: 'textScore' } })
				.exec();
		} catch (error) {
			// Fallback to regex search if text index is not available
			const regexQuery: any = {
				term: { $regex: term, $options: 'i' },
			};
			
			if (language) {
				regexQuery.language = language;
			}
			
			return await this.model.find(regexQuery).limit(limit).exec();
		}
	}

	async findOrCreateWords(terms: string[], language: Language): Promise<WordDocument[]> {
		try {
			const words: WordDocument[] = [];
			
			for (const term of terms) {
				let word = await this.findByTerm(term, language);
				
				if (!word) {
					word = await this.create({
						term,
						language,
						definitions: [],
					}) as WordDocument;
				}
				
				words.push(word);
			}
			
			return words;
		} catch (error) {
			throw new Error(`Failed to find or create words: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findWordsByIds(wordIds: string[]): Promise<WordDocument[]> {
		try {
			const objectIds = wordIds.map(id => new Types.ObjectId(id));
			return await this.model.find({ _id: { $in: objectIds } }).exec();
		} catch (error) {
			throw new Error(`Failed to find words by IDs: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByTerm(term: string, language: Language): Promise<WordDocument | null> {
		try {
			return await this.model.findOne({ term, language }).exec();
		} catch (error) {
			throw new Error(`Failed to find word by term: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Additional MongoDB-specific methods
	async findWordsByLanguage(language: Language, limit?: number): Promise<WordDocument[]> {
		try {
			const query = this.model.find({ language });
			if (limit) {
				query.limit(limit);
			}
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find words by language: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findWordsWithDefinitions(language?: Language, limit?: number): Promise<WordDocument[]> {
		try {
			const filter: any = {
				'definitions.0': { $exists: true }, // Has at least one definition
			};
			
			if (language) {
				filter.language = language;
			}
			
			const query = this.model.find(filter);
			if (limit) {
				query.limit(limit);
			}
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find words with definitions: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findWordsWithoutDefinitions(language?: Language, limit?: number): Promise<WordDocument[]> {
		try {
			const filter: any = {
				$or: [
					{ definitions: { $size: 0 } },
					{ definitions: { $exists: false } },
				],
			};
			
			if (language) {
				filter.language = language;
			}
			
			const query = this.model.find(filter);
			if (limit) {
				query.limit(limit);
			}
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find words without definitions: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findWordsWithAudio(language?: Language, limit?: number): Promise<WordDocument[]> {
		try {
			const filter: any = {
				audio_url: { $exists: true, $ne: null, $ne: '' },
			};
			
			if (language) {
				filter.language = language;
			}
			
			const query = this.model.find(filter);
			if (limit) {
				query.limit(limit);
			}
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find words with audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async countWordsByLanguage(): Promise<Record<Language, number>> {
		try {
			const pipeline = [
				{
					$group: {
						_id: '$language',
						count: { $sum: 1 },
					},
				},
			];

			const results = await this.model.aggregate(pipeline).exec();
			
			// Initialize counts for all languages
			const counts: Record<Language, number> = {
				[Language.EN]: 0,
				[Language.VI]: 0,
			};

			// Fill in actual counts
			for (const result of results) {
				if (result._id in counts) {
					counts[result._id as Language] = result.count;
				}
			}

			return counts;
		} catch (error) {
			throw new Error(`Failed to count words by language: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findRecentWords(days: number = 7, limit: number = 10): Promise<WordDocument[]> {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - days);

			return await this.model.find({
				created_at: { $gte: cutoffDate },
			})
			.sort({ created_at: -1 })
			.limit(limit)
			.exec();
		} catch (error) {
			throw new Error(`Failed to find recent words: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addDefinitionToWord(wordId: string, definition: any): Promise<WordDocument> {
		try {
			const word = await this.model.findById(wordId).exec();
			if (!word) {
				throw new Error('Word not found');
			}

			word.definitions.push(definition);
			return await word.save();
		} catch (error) {
			throw new Error(`Failed to add definition to word: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async updateDefinition(wordId: string, definitionId: string, update: any): Promise<WordDocument> {
		try {
			const word = await this.model.findById(wordId).exec();
			if (!word) {
				throw new Error('Word not found');
			}

			const definition = word.definitions.id(definitionId);
			if (!definition) {
				throw new Error('Definition not found');
			}

			Object.assign(definition, update);
			return await word.save();
		} catch (error) {
			throw new Error(`Failed to update definition: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async removeDefinition(wordId: string, definitionId: string): Promise<WordDocument> {
		try {
			const word = await this.model.findById(wordId).exec();
			if (!word) {
				throw new Error('Word not found');
			}

			word.definitions.pull({ _id: definitionId });
			return await word.save();
		} catch (error) {
			throw new Error(`Failed to remove definition: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addExampleToDefinition(wordId: string, definitionId: string, example: any): Promise<WordDocument> {
		try {
			const word = await this.model.findById(wordId).exec();
			if (!word) {
				throw new Error('Word not found');
			}

			const definition = word.definitions.id(definitionId);
			if (!definition) {
				throw new Error('Definition not found');
			}

			definition.examples.push(example);
			return await word.save();
		} catch (error) {
			throw new Error(`Failed to add example to definition: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addExplainToDefinition(wordId: string, definitionId: string, explain: any): Promise<WordDocument> {
		try {
			const word = await this.model.findById(wordId).exec();
			if (!word) {
				throw new Error('Word not found');
			}

			const definition = word.definitions.id(definitionId);
			if (!definition) {
				throw new Error('Definition not found');
			}

			definition.explains.push(explain);
			return await word.save();
		} catch (error) {
			throw new Error(`Failed to add explain to definition: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}
}
