import { Model, Document, FilterQuery, UpdateQuery, Types } from 'mongoose';
import { errorLogger, createError<PERSON>ontext, ServerError } from '@/lib/error-handling';
import { BaseRepository } from './base.repository';

// MongoDB-specific base document interface
export interface MongoBaseDocument extends Document {
	_id: Types.ObjectId;
	id: string;
	created_at: Date;
	updated_at: Date;
}

// MongoDB repository interface extending the base repository
export interface BaseMongoRepository<T extends MongoBaseDocument> extends BaseRepository<T> {
	findByFilter(filter: FilterQuery<T>): Promise<T[]>;
	findOneByFilter(filter: FilterQuery<T>): Promise<T | null>;
	updateByFilter(filter: FilterQuery<T>, update: UpdateQuery<T>): Promise<T[]>;
	deleteByFilter(filter: FilterQuery<T>): Promise<void>;
	count(filter?: FilterQuery<T>): Promise<number>;
	exists(filter: FilterQuery<T>): Promise<boolean>;
}

// Base MongoDB repository implementation
export class BaseMongoRepositoryImpl<T extends MongoBaseDocument> implements BaseMongoRepository<T> {
	constructor(protected readonly model: Model<T>) {}

	async findById(id: string): Promise<T | null> {
		try {
			// Handle both ObjectId and string formats
			const objectId = Types.ObjectId.isValid(id) ? new Types.ObjectId(id) : id;
			return await this.model.findById(objectId).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB query failed in findById',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find_by_id', { id }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async findOne(query: Record<string, unknown>): Promise<T | null> {
		try {
			return await this.model.findOne(query as FilterQuery<T>).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB query failed in findOne',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find_one', { query }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async find(query: Record<string, unknown>): Promise<T[]> {
		try {
			return await this.model.find(query as FilterQuery<T>).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB query failed in find',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find', { query }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async create(data: Record<string, unknown>): Promise<T> {
		try {
			const document = new this.model(data);
			return await document.save();
		} catch (error) {
			errorLogger.error(
				'MongoDB create operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'create', { data }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async update(id: string, data: Record<string, unknown>): Promise<T> {
		try {
			const objectId = Types.ObjectId.isValid(id) ? new Types.ObjectId(id) : id;
			const updated = await this.model.findByIdAndUpdate(
				objectId,
				data as UpdateQuery<T>,
				{ new: true, runValidators: true }
			).exec();
			
			if (!updated) {
				throw new ServerError('Document not found');
			}
			
			return updated;
		} catch (error) {
			errorLogger.error(
				'MongoDB update operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'update', { id, data }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async delete(query: Record<string, unknown>): Promise<void> {
		try {
			await this.model.deleteMany(query as FilterQuery<T>).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB delete operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'delete', { query }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	// MongoDB-specific methods
	async findByFilter(filter: FilterQuery<T>): Promise<T[]> {
		try {
			return await this.model.find(filter).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB findByFilter operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find_by_filter', { filter }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async findOneByFilter(filter: FilterQuery<T>): Promise<T | null> {
		try {
			return await this.model.findOne(filter).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB findOneByFilter operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find_one_by_filter', { filter }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async updateByFilter(filter: FilterQuery<T>, update: UpdateQuery<T>): Promise<T[]> {
		try {
			await this.model.updateMany(filter, update).exec();
			// Return updated documents
			return await this.model.find(filter).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB updateByFilter operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'update_by_filter', { filter, update }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async deleteByFilter(filter: FilterQuery<T>): Promise<void> {
		try {
			await this.model.deleteMany(filter).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB deleteByFilter operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'delete_by_filter', { filter }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async count(filter?: FilterQuery<T>): Promise<number> {
		try {
			return await this.model.countDocuments(filter || {}).exec();
		} catch (error) {
			errorLogger.error(
				'MongoDB count operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'count', { filter }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async exists(filter: FilterQuery<T>): Promise<boolean> {
		try {
			const result = await this.model.exists(filter).exec();
			return result !== null;
		} catch (error) {
			errorLogger.error(
				'MongoDB exists operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'exists', { filter }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	// Utility methods for common operations
	async findWithPagination(
		filter: FilterQuery<T>,
		page: number = 1,
		limit: number = 10,
		sort?: Record<string, 1 | -1>
	): Promise<{ documents: T[]; total: number; page: number; totalPages: number }> {
		try {
			const skip = (page - 1) * limit;
			const query = this.model.find(filter).skip(skip).limit(limit);
			
			if (sort) {
				query.sort(sort);
			}
			
			const [documents, total] = await Promise.all([
				query.exec(),
				this.model.countDocuments(filter).exec(),
			]);
			
			return {
				documents,
				total,
				page,
				totalPages: Math.ceil(total / limit),
			};
		} catch (error) {
			errorLogger.error(
				'MongoDB pagination operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'find_with_pagination', { filter, page, limit, sort }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}

	async upsert(filter: FilterQuery<T>, update: UpdateQuery<T>): Promise<T> {
		try {
			const result = await this.model.findOneAndUpdate(
				filter,
				update,
				{ new: true, upsert: true, runValidators: true }
			).exec();
			
			if (!result) {
				throw new ServerError('Upsert operation failed');
			}
			
			return result;
		} catch (error) {
			errorLogger.error(
				'MongoDB upsert operation failed',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('BaseMongoRepository', 'upsert', { filter, update }),
				'BaseMongoRepository'
			);
			throw new ServerError('Database operation failed');
		}
	}
}
