import { Types } from 'mongoose';
import { BaseMongoRepositoryImpl, MongoBaseDocument } from './base.mongo.repository';
import { CollectionRepository } from './collection.repository';
import { CollectionModel, ICollection } from '@/backend/schemas';

// Extend ICollection to include MongoBaseDocument properties
interface CollectionDocument extends ICollection, MongoBaseDocument {}

export class CollectionMongoRepositoryImpl extends BaseMongoRepositoryImpl<CollectionDocument> implements CollectionRepository {
	constructor() {
		super(CollectionModel as any);
	}

	// Override base methods to ensure proper typing
	override async findById(id: string): Promise<CollectionDocument | null> {
		return super.findById(id) as Promise<CollectionDocument | null>;
	}

	override async findOne(query: Record<string, unknown>): Promise<CollectionDocument | null> {
		return super.findOne(query) as Promise<CollectionDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<CollectionDocument[]> {
		return super.find(query) as Promise<CollectionDocument[]>;
	}

	override async create(data: Record<string, unknown>): Promise<CollectionDocument> {
		const collectionData = data as Partial<ICollection>;
		
		if (!collectionData.name || !collectionData.user_id) {
			throw new Error('Name and user_id are required');
		}

		return super.create(data) as Promise<CollectionDocument>;
	}

	override async update(id: string, data: Record<string, unknown>): Promise<CollectionDocument> {
		return super.update(id, data) as Promise<CollectionDocument>;
	}

	// CollectionRepository interface methods
	async findUserCollections(userId: string): Promise<CollectionDocument[]> {
		try {
			return await this.model.find({ 
				user_id: new Types.ObjectId(userId) 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find user collections: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByUserIdAndName(userId: string, name: string): Promise<CollectionDocument | null> {
		try {
			return await this.model.findOne({ 
				user_id: new Types.ObjectId(userId), 
				name 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collection by user ID and name: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Additional MongoDB-specific methods
	async findCollectionsWithWords(wordIds: string[]): Promise<CollectionDocument[]> {
		try {
			const objectIds = wordIds.map(id => new Types.ObjectId(id));
			return await this.model.find({ 
				word_ids: { $in: objectIds } 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collections with words: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findCollectionsWithParagraphs(paragraphIds: string[]): Promise<CollectionDocument[]> {
		try {
			const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
			return await this.model.find({ 
				paragraph_ids: { $in: objectIds } 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collections with paragraphs: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findCollectionsWithKeywords(keywordIds: string[]): Promise<CollectionDocument[]> {
		try {
			const objectIds = keywordIds.map(id => new Types.ObjectId(id));
			return await this.model.find({ 
				keyword_ids: { $in: objectIds } 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collections with keywords: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addWordsToCollection(collectionId: string, wordIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = wordIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$addToSet: { word_ids: { $each: objectIds } } // $addToSet prevents duplicates
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to add words to collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async removeWordsFromCollection(collectionId: string, wordIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = wordIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$pull: { word_ids: { $in: objectIds } }
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to remove words from collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addParagraphsToCollection(collectionId: string, paragraphIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$addToSet: { paragraph_ids: { $each: objectIds } }
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to add paragraphs to collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async removeParagraphsFromCollection(collectionId: string, paragraphIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = paragraphIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$pull: { paragraph_ids: { $in: objectIds } }
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to remove paragraphs from collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async addKeywordsToCollection(collectionId: string, keywordIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = keywordIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$addToSet: { keyword_ids: { $each: objectIds } }
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to add keywords to collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async removeKeywordsFromCollection(collectionId: string, keywordIds: string[]): Promise<CollectionDocument> {
		try {
			const objectIds = keywordIds.map(id => new Types.ObjectId(id));
			const updated = await this.model.findByIdAndUpdate(
				collectionId,
				{ 
					$pull: { keyword_ids: { $in: objectIds } }
				},
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Collection not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to remove keywords from collection: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async getCollectionStats(collectionId: string): Promise<{
		wordCount: number;
		paragraphCount: number;
		keywordCount: number;
	}> {
		try {
			const collection = await this.model.findById(collectionId).exec();
			if (!collection) {
				throw new Error('Collection not found');
			}

			return {
				wordCount: collection.word_ids.length,
				paragraphCount: collection.paragraph_ids.length,
				keywordCount: collection.keyword_ids.length,
			};
		} catch (error) {
			throw new Error(`Failed to get collection stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findCollectionsByLanguage(targetLanguage?: string, sourceLanguage?: string): Promise<CollectionDocument[]> {
		try {
			const filter: any = {};
			
			if (targetLanguage) {
				filter.target_language = targetLanguage;
			}
			
			if (sourceLanguage) {
				filter.source_language = sourceLanguage;
			}

			return await this.model.find(filter).exec();
		} catch (error) {
			throw new Error(`Failed to find collections by language: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findRecentCollections(days: number = 7, limit: number = 10): Promise<CollectionDocument[]> {
		try {
			const cutoffDate = new Date();
			cutoffDate.setDate(cutoffDate.getDate() - days);

			return await this.model.find({
				created_at: { $gte: cutoffDate },
			})
			.sort({ created_at: -1 })
			.limit(limit)
			.exec();
		} catch (error) {
			throw new Error(`Failed to find recent collections: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findPopularCollections(limit: number = 10): Promise<CollectionDocument[]> {
		try {
			// Sort by total content count (words + paragraphs + keywords)
			const pipeline = [
				{
					$addFields: {
						totalContent: {
							$add: [
								{ $size: '$word_ids' },
								{ $size: '$paragraph_ids' },
								{ $size: '$keyword_ids' },
							],
						},
					},
				},
				{
					$sort: { totalContent: -1 },
				},
				{
					$limit: limit,
				},
			];

			return await this.model.aggregate(pipeline).exec();
		} catch (error) {
			throw new Error(`Failed to find popular collections: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async searchCollections(searchTerm: string, userId?: string, limit: number = 10): Promise<CollectionDocument[]> {
		try {
			const filter: any = {
				name: { $regex: searchTerm, $options: 'i' },
			};
			
			if (userId) {
				filter.user_id = new Types.ObjectId(userId);
			}

			return await this.model.find(filter).limit(limit).exec();
		} catch (error) {
			throw new Error(`Failed to search collections: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async toggleNotification(collectionId: string): Promise<CollectionDocument> {
		try {
			const collection = await this.model.findById(collectionId).exec();
			if (!collection) {
				throw new Error('Collection not found');
			}

			collection.enable_learn_word_notification = !collection.enable_learn_word_notification;
			return await collection.save();
		} catch (error) {
			throw new Error(`Failed to toggle notification: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}
}
