import { Types } from 'mongoose';
import { BaseMongoRepositoryImpl, MongoBaseDocument } from './base.mongo.repository';
import { 
	ParagraphRepository, 
	KeywordRepository, 
	LastSeenWordRepository,
	CollectionStatsRepository 
} from './';
import { 
	ParagraphModel, 
	KeywordModel, 
	LastSeenWordModel, 
	CollectionStatsModel,
	FeedbackModel,
	IParagraph, 
	IKeyword, 
	ILastSeenWord, 
	ICollectionStats,
	IFeedback,
	Language, 
	Difficulty 
} from '@/backend/schemas';
import { BaseRepository } from './base.repository';

// Document interfaces
interface ParagraphDocument extends IParagraph, MongoBaseDocument {}
interface KeywordDocument extends IKeyword, MongoBaseDocument {}
interface LastSeenWordDocument extends ILastSeenWord, MongoBaseDocument {}
interface CollectionStatsDocument extends ICollectionStats, MongoBaseDocument {}
interface FeedbackDocument extends IFeedback, MongoBaseDocument {}

// Paragraph MongoDB Repository
export class ParagraphMongoRepositoryImpl extends BaseMongoRepositoryImpl<ParagraphDocument> implements ParagraphRepository {
	constructor() {
		super(ParagraphModel as any);
	}

	async findByLanguage(language: Language, limit?: number): Promise<ParagraphDocument[]> {
		try {
			const query = this.model.find({ language });
			if (limit) query.limit(limit);
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find paragraphs by language: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByDifficulty(difficulty: Difficulty, limit?: number): Promise<ParagraphDocument[]> {
		try {
			const query = this.model.find({ difficulty });
			if (limit) query.limit(limit);
			return await query.exec();
		} catch (error) {
			throw new Error(`Failed to find paragraphs by difficulty: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByIds(ids: string[]): Promise<ParagraphDocument[]> {
		try {
			const objectIds = ids.map(id => new Types.ObjectId(id));
			return await this.model.find({ _id: { $in: objectIds } }).exec();
		} catch (error) {
			throw new Error(`Failed to find paragraphs by IDs: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async createMany(data: any[]): Promise<ParagraphDocument[]> {
		try {
			return await this.model.insertMany(data);
		} catch (error) {
			throw new Error(`Failed to create many paragraphs: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findWithExercises(id: string): Promise<ParagraphDocument | null> {
		try {
			return await this.model.findById(id).exec();
		} catch (error) {
			throw new Error(`Failed to find paragraph with exercises: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Override base methods for proper typing
	override async findById(id: string): Promise<ParagraphDocument | null> {
		return super.findById(id) as Promise<ParagraphDocument | null>;
	}

	override async find(query: Record<string, unknown>, limit?: number): Promise<ParagraphDocument[]> {
		const mongoQuery = this.model.find(query);
		if (limit) mongoQuery.limit(limit);
		return await mongoQuery.exec();
	}
}

// Keyword MongoDB Repository
export class KeywordMongoRepositoryImpl extends BaseMongoRepositoryImpl<KeywordDocument> implements KeywordRepository {
	constructor() {
		super(KeywordModel as any);
	}

	async findUserKeywords(userId: string): Promise<KeywordDocument[]> {
		try {
			return await this.model.find({ user_id: new Types.ObjectId(userId) }).exec();
		} catch (error) {
			throw new Error(`Failed to find user keywords: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async search(query: string, userId?: string): Promise<KeywordDocument[]> {
		try {
			const searchFilter: any = {
				content: { $regex: query, $options: 'i' },
			};
			
			if (userId) {
				searchFilter.user_id = new Types.ObjectId(userId);
			}
			
			return await this.model.find(searchFilter).exec();
		} catch (error) {
			throw new Error(`Failed to search keywords: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Override base methods for proper typing
	override async findById(id: string): Promise<KeywordDocument | null> {
		return super.findById(id) as Promise<KeywordDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<KeywordDocument[]> {
		return super.find(query) as Promise<KeywordDocument[]>;
	}
}

// LastSeenWord MongoDB Repository
export class LastSeenWordMongoRepositoryImpl extends BaseMongoRepositoryImpl<LastSeenWordDocument> implements LastSeenWordRepository {
	constructor() {
		super(LastSeenWordModel as any);
	}

	async findLastSeenWord(userId: string, wordId: string): Promise<LastSeenWordDocument | null> {
		try {
			return await this.model.findOne({ 
				user_id: new Types.ObjectId(userId), 
				word_id: new Types.ObjectId(wordId) 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find last seen word: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findLastSeenWordsByWordIds(userId: string, wordIds: string[]): Promise<LastSeenWordDocument[]> {
		try {
			const objectIds = wordIds.map(id => new Types.ObjectId(id));
			return await this.model.find({ 
				user_id: new Types.ObjectId(userId), 
				word_id: { $in: objectIds } 
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find last seen words by word IDs: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async updateOrCreateLastSeenWord(userId: string, wordId: string): Promise<LastSeenWordDocument> {
		try {
			return await this.model.findOneAndUpdate(
				{ 
					user_id: new Types.ObjectId(userId), 
					word_id: new Types.ObjectId(wordId) 
				},
				{
					$set: { last_seen_at: new Date() },
					$inc: { review_count: 1 },
					$setOnInsert: { 
						user_id: new Types.ObjectId(userId), 
						word_id: new Types.ObjectId(wordId) 
					}
				},
				{ 
					upsert: true, 
					new: true 
				}
			).exec() as LastSeenWordDocument;
		} catch (error) {
			throw new Error(`Failed to update or create last seen word: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Override base methods for proper typing
	override async findById(id: string): Promise<LastSeenWordDocument | null> {
		return super.findById(id) as Promise<LastSeenWordDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<LastSeenWordDocument[]> {
		return super.find(query) as Promise<LastSeenWordDocument[]>;
	}
}

// CollectionStats MongoDB Repository
export class CollectionStatsMongoRepositoryImpl extends BaseMongoRepositoryImpl<CollectionStatsDocument> implements CollectionStatsRepository {
	constructor() {
		super(CollectionStatsModel as any);
	}

	async findByCollectionAndUser(collectionId: string, userId: string): Promise<CollectionStatsDocument[]> {
		try {
			return await this.model.find({ 
				collection_id: new Types.ObjectId(collectionId),
				user_id: new Types.ObjectId(userId)
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collection stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByDateRange(collectionId: string, userId: string, startDate: Date, endDate: Date): Promise<CollectionStatsDocument[]> {
		try {
			return await this.model.find({
				collection_id: new Types.ObjectId(collectionId),
				user_id: new Types.ObjectId(userId),
				date: { $gte: startDate, $lte: endDate }
			}).exec();
		} catch (error) {
			throw new Error(`Failed to find collection stats by date range: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async upsertStats(collectionId: string, userId: string, date: Date, stats: Partial<ICollectionStats>): Promise<CollectionStatsDocument> {
		try {
			return await this.model.findOneAndUpdate(
				{
					collection_id: new Types.ObjectId(collectionId),
					user_id: new Types.ObjectId(userId),
					date: date
				},
				{
					$inc: {
						words_reviewed_count: stats.words_reviewed_count || 0,
						qa_practice_submissions: stats.qa_practice_submissions || 0,
						paragraph_practice_submissions: stats.paragraph_practice_submissions || 0,
					},
					$setOnInsert: {
						collection_id: new Types.ObjectId(collectionId),
						user_id: new Types.ObjectId(userId),
						date: date
					}
				},
				{ upsert: true, new: true }
			).exec() as CollectionStatsDocument;
		} catch (error) {
			throw new Error(`Failed to upsert collection stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	// Override base methods for proper typing
	override async findById(id: string): Promise<CollectionStatsDocument | null> {
		return super.findById(id) as Promise<CollectionStatsDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<CollectionStatsDocument[]> {
		return super.find(query) as Promise<CollectionStatsDocument[]>;
	}
}

// Feedback MongoDB Repository (BaseRepository implementation)
export class FeedbackMongoRepositoryImpl extends BaseMongoRepositoryImpl<FeedbackDocument> implements BaseRepository<FeedbackDocument> {
	constructor() {
		super(FeedbackModel as any);
	}

	// Override base methods for proper typing
	override async findById(id: string): Promise<FeedbackDocument | null> {
		return super.findById(id) as Promise<FeedbackDocument | null>;
	}

	override async find(query: Record<string, unknown>): Promise<FeedbackDocument[]> {
		return super.find(query) as Promise<FeedbackDocument[]>;
	}

	// Additional methods for feedback
	async findByUser(userId: string): Promise<FeedbackDocument[]> {
		try {
			return await this.model.find({ user_id: new Types.ObjectId(userId) }).exec();
		} catch (error) {
			throw new Error(`Failed to find feedback by user: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async findByStatus(status: string): Promise<FeedbackDocument[]> {
		try {
			return await this.model.find({ status }).exec();
		} catch (error) {
			throw new Error(`Failed to find feedback by status: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async updateStatus(id: string, status: string): Promise<FeedbackDocument> {
		try {
			const updated = await this.model.findByIdAndUpdate(
				id,
				{ status },
				{ new: true, runValidators: true }
			).exec();

			if (!updated) {
				throw new Error('Feedback not found');
			}

			return updated;
		} catch (error) {
			throw new Error(`Failed to update feedback status: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}
}
