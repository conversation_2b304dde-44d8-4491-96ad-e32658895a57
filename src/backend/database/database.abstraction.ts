'use server';

import { PrismaClient } from '@prisma/client';
import { connectToMongoDB, disconnectFromMongoDB } from '@/config/mongodb';

// Database types
export enum DatabaseType {
	POSTGRESQL = 'postgresql',
	MONGODB = 'mongodb',
}

// Database configuration interface
export interface DatabaseConfig {
	type: DatabaseType;
	enabled: boolean;
	primary: boolean;
}

// Database abstraction interface
export interface DatabaseAbstraction {
	connect(): Promise<void>;
	disconnect(): Promise<void>;
	isConnected(): boolean;
	getType(): DatabaseType;
	healthCheck(): Promise<'healthy' | 'unhealthy' | 'unknown'>;
}

// PostgreSQL implementation
export class PostgreSQLDatabase implements DatabaseAbstraction {
	private prisma: PrismaClient | null = null;
	private connected = false;

	async connect(): Promise<void> {
		try {
			if (!this.prisma) {
				this.prisma = new PrismaClient();
			}
			await this.prisma.$connect();
			this.connected = true;
		} catch (error) {
			this.connected = false;
			throw new Error(`Failed to connect to PostgreSQL: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async disconnect(): Promise<void> {
		try {
			if (this.prisma) {
				await this.prisma.$disconnect();
				this.connected = false;
			}
		} catch (error) {
			throw new Error(`Failed to disconnect from PostgreSQL: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	isConnected(): boolean {
		return this.connected;
	}

	getType(): DatabaseType {
		return DatabaseType.POSTGRESQL;
	}

	async healthCheck(): Promise<'healthy' | 'unhealthy' | 'unknown'> {
		try {
			if (!this.prisma || !this.connected) {
				return 'unhealthy';
			}
			await this.prisma.$queryRaw`SELECT 1`;
			return 'healthy';
		} catch (error) {
			return 'unhealthy';
		}
	}

	getPrismaClient(): PrismaClient {
		if (!this.prisma) {
			throw new Error('PostgreSQL not connected');
		}
		return this.prisma;
	}
}

// MongoDB implementation
export class MongoDatabase implements DatabaseAbstraction {
	private connected = false;

	async connect(): Promise<void> {
		try {
			await connectToMongoDB();
			this.connected = true;
		} catch (error) {
			this.connected = false;
			throw new Error(`Failed to connect to MongoDB: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	async disconnect(): Promise<void> {
		try {
			await disconnectFromMongoDB();
			this.connected = false;
		} catch (error) {
			throw new Error(`Failed to disconnect from MongoDB: ${error instanceof Error ? error.message : 'Unknown error'}`);
		}
	}

	isConnected(): boolean {
		return this.connected;
	}

	getType(): DatabaseType {
		return DatabaseType.MONGODB;
	}

	async healthCheck(): Promise<'healthy' | 'unhealthy' | 'unknown'> {
		try {
			const { checkMongoDBHealth } = await import('@/config/mongodb');
			return await checkMongoDBHealth();
		} catch (error) {
			return 'unhealthy';
		}
	}
}

// Database manager class
export class DatabaseManager {
	private databases: Map<DatabaseType, DatabaseAbstraction> = new Map();
	private primaryDatabase: DatabaseType | null = null;
	private dualModeEnabled = false;

	constructor() {
		this.initializeDatabases();
	}

	private initializeDatabases(): void {
		// Initialize PostgreSQL
		this.databases.set(DatabaseType.POSTGRESQL, new PostgreSQLDatabase());
		
		// Initialize MongoDB
		this.databases.set(DatabaseType.MONGODB, new MongoDatabase());
		
		// Set primary database based on feature flags
		this.updateConfiguration();
	}

	private updateConfiguration(): void {
		const mongoEnabled = process.env.FEATURE_MONGODB_ENABLED === 'true';
		const dualDatabase = process.env.FEATURE_DUAL_DATABASE === 'true';
		
		this.dualModeEnabled = dualDatabase;
		
		if (mongoEnabled) {
			this.primaryDatabase = DatabaseType.MONGODB;
		} else {
			this.primaryDatabase = DatabaseType.POSTGRESQL;
		}
	}

	async connectPrimary(): Promise<void> {
		if (!this.primaryDatabase) {
			throw new Error('No primary database configured');
		}

		const database = this.databases.get(this.primaryDatabase);
		if (!database) {
			throw new Error(`Primary database ${this.primaryDatabase} not found`);
		}

		await database.connect();
	}

	async connectAll(): Promise<void> {
		const promises: Promise<void>[] = [];
		
		if (this.dualModeEnabled) {
			// Connect to all databases in dual mode
			for (const database of this.databases.values()) {
				promises.push(database.connect());
			}
		} else {
			// Connect only to primary database
			promises.push(this.connectPrimary());
		}

		await Promise.all(promises);
	}

	async disconnectAll(): Promise<void> {
		const promises: Promise<void>[] = [];
		
		for (const database of this.databases.values()) {
			if (database.isConnected()) {
				promises.push(database.disconnect());
			}
		}

		await Promise.all(promises);
	}

	getPrimaryDatabase(): DatabaseAbstraction | null {
		if (!this.primaryDatabase) {
			return null;
		}
		return this.databases.get(this.primaryDatabase) || null;
	}

	getDatabase(type: DatabaseType): DatabaseAbstraction | null {
		return this.databases.get(type) || null;
	}

	getPrimaryDatabaseType(): DatabaseType | null {
		return this.primaryDatabase;
	}

	isDualModeEnabled(): boolean {
		return this.dualModeEnabled;
	}

	async healthCheck(): Promise<Record<DatabaseType, 'healthy' | 'unhealthy' | 'unknown'>> {
		const results: Record<DatabaseType, 'healthy' | 'unhealthy' | 'unknown'> = {
			[DatabaseType.POSTGRESQL]: 'unknown',
			[DatabaseType.MONGODB]: 'unknown',
		};

		const promises = Array.from(this.databases.entries()).map(async ([type, database]) => {
			try {
				results[type] = await database.healthCheck();
			} catch (error) {
				results[type] = 'unhealthy';
			}
		});

		await Promise.all(promises);
		return results;
	}

	getConnectionStatus(): Record<DatabaseType, boolean> {
		const status: Record<DatabaseType, boolean> = {
			[DatabaseType.POSTGRESQL]: false,
			[DatabaseType.MONGODB]: false,
		};

		for (const [type, database] of this.databases.entries()) {
			status[type] = database.isConnected();
		}

		return status;
	}

	// Utility methods for feature flag checks
	isMongoDBEnabled(): boolean {
		return process.env.FEATURE_MONGODB_ENABLED === 'true';
	}

	isPostgreSQLEnabled(): boolean {
		return !this.isMongoDBEnabled() || this.dualModeEnabled;
	}

	shouldUseMongoDB(): boolean {
		return this.primaryDatabase === DatabaseType.MONGODB;
	}

	shouldUsePostgreSQL(): boolean {
		return this.primaryDatabase === DatabaseType.POSTGRESQL;
	}

	// Get specific database instances
	getPostgreSQLDatabase(): PostgreSQLDatabase | null {
		const db = this.databases.get(DatabaseType.POSTGRESQL);
		return db instanceof PostgreSQLDatabase ? db : null;
	}

	getMongoDatabase(): MongoDatabase | null {
		const db = this.databases.get(DatabaseType.MONGODB);
		return db instanceof MongoDatabase ? db : null;
	}
}

// Singleton instance
let databaseManager: DatabaseManager | null = null;

export function getDatabaseManager(): DatabaseManager {
	if (!databaseManager) {
		databaseManager = new DatabaseManager();
	}
	return databaseManager;
}

// Utility functions for easy access
export async function connectToPrimaryDatabase(): Promise<void> {
	const manager = getDatabaseManager();
	await manager.connectPrimary();
}

export async function connectToAllDatabases(): Promise<void> {
	const manager = getDatabaseManager();
	await manager.connectAll();
}

export async function disconnectFromAllDatabases(): Promise<void> {
	const manager = getDatabaseManager();
	await manager.disconnectAll();
}

export function isPrimaryDatabaseMongoDB(): boolean {
	const manager = getDatabaseManager();
	return manager.shouldUseMongoDB();
}

export function isPrimaryDatabasePostgreSQL(): boolean {
	const manager = getDatabaseManager();
	return manager.shouldUsePostgreSQL();
}

export function isDualDatabaseMode(): boolean {
	const manager = getDatabaseManager();
	return manager.isDualModeEnabled();
}
