'use client';

import { useState } from 'react';
import { Button, Translate } from '@/components/ui';
import { useDOMFloating } from '@/hooks/use-dom-floating';
import { Settings, X } from 'lucide-react';

export function DOMClientSettings() {
	const [isOpen, setIsOpen] = useState(false);

	// Settings button
	const settingsButton = (
		<Button
			size="icon"
			className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105"
			onClick={() => setIsOpen(true)}
		>
			<Settings className="h-6 w-6" />
		</Button>
	);

	// Settings panel
	const settingsPanel = (
		<div className="bg-background border rounded-lg shadow-lg w-64 p-4">
			<div className="flex items-center justify-between mb-4">
				<h3 className="text-lg font-semibold">
					<Translate text="settings.title" />
				</h3>
				<Button
					size="icon"
					variant="ghost"
					className="h-8 w-8"
					onClick={() => setIsOpen(false)}
				>
					<X className="h-4 w-4" />
				</Button>
			</div>
			<div className="space-y-2">
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.language" />
				</Button>
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.theme" />
				</Button>
				<Button size="sm" variant="outline" className="w-full">
					<Translate text="settings.account" />
				</Button>
			</div>
		</div>
	);

	// Use DOM floating for button
	useDOMFloating('settings-button', settingsButton, {
		position: { bottom: 16, right: 16 },
		zIndex: 1200,
		autoShow: !isOpen,
	});

	// Use DOM floating for panel
	useDOMFloating('settings-panel', settingsPanel, {
		position: { bottom: 16, right: 16 },
		zIndex: 1201,
		autoShow: isOpen,
	});

	return null; // Content is rendered through DOM floating system
}
