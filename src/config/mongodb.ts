'use server';

import mongoose from 'mongoose';
import { errorLogger, createErrorContext } from '@/lib/error-handling';

// MongoDB connection configuration
interface MongoDBConfig {
	uri: string;
	options: mongoose.ConnectOptions;
}

// Global connection instance
let isConnected = false;
let connectionPromise: Promise<typeof mongoose> | null = null;

/**
 * Get MongoDB configuration from environment variables
 */
export async function getMongoDBConfig(): Promise<MongoDBConfig> {
	const uri = process.env.MONGODB_URI || process.env.DATABASE_URL_MONGODB || 'mongodb://localhost:27017/vocab';
	
	const options: mongoose.ConnectOptions = {
		// Connection pool settings
		maxPoolSize: Number(process.env.MONGODB_MAX_POOL_SIZE) || 10,
		minPoolSize: Number(process.env.MONGODB_MIN_POOL_SIZE) || 2,
		maxIdleTimeMS: Number(process.env.MONGODB_MAX_IDLE_TIME_MS) || 30000,
		serverSelectionTimeoutMS: Number(process.env.MONGODB_SERVER_SELECTION_TIMEOUT_MS) || 5000,
		socketTimeoutMS: Number(process.env.MONGODB_SOCKET_TIMEOUT_MS) || 45000,
		
		// Buffering settings
		bufferCommands: false,
		bufferMaxEntries: 0,
		
		// Retry settings
		retryWrites: true,
		retryReads: true,
		
		// Compression
		compressors: ['zlib'],
		
		// Application name for monitoring
		appName: 'vocab-app',
	};

	return { uri, options };
}

/**
 * Connect to MongoDB with proper error handling and connection pooling
 */
export async function connectToMongoDB(): Promise<typeof mongoose> {
	try {
		// Return existing connection if already connected
		if (isConnected && mongoose.connection.readyState === 1) {
			return mongoose;
		}

		// Return existing connection promise if connection is in progress
		if (connectionPromise) {
			return connectionPromise;
		}

		const config = await getMongoDBConfig();
		
		// Create new connection promise
		connectionPromise = mongoose.connect(config.uri, config.options);
		
		const mongooseInstance = await connectionPromise;
		
		// Set up connection event listeners
		mongoose.connection.on('connected', () => {
			isConnected = true;
			console.log('MongoDB connected successfully');
		});

		mongoose.connection.on('error', (error) => {
			isConnected = false;
			errorLogger.error(
				'MongoDB connection error',
				error instanceof Error ? error : new Error(String(error)),
				createErrorContext('MongoDB', 'connection_error'),
				'MongoDB'
			);
		});

		mongoose.connection.on('disconnected', () => {
			isConnected = false;
			console.log('MongoDB disconnected');
		});

		// Handle application termination
		process.on('SIGINT', async () => {
			await disconnectFromMongoDB();
			process.exit(0);
		});

		return mongooseInstance;
	} catch (error) {
		isConnected = false;
		connectionPromise = null;
		
		errorLogger.error(
			'Failed to connect to MongoDB',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('MongoDB', 'connection_failed'),
			'MongoDB'
		);
		
		throw error;
	}
}

/**
 * Disconnect from MongoDB
 */
export async function disconnectFromMongoDB(): Promise<void> {
	try {
		if (mongoose.connection.readyState !== 0) {
			await mongoose.disconnect();
			isConnected = false;
			connectionPromise = null;
			console.log('MongoDB disconnected successfully');
		}
	} catch (error) {
		errorLogger.error(
			'Error disconnecting from MongoDB',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('MongoDB', 'disconnection_error'),
			'MongoDB'
		);
		throw error;
	}
}

/**
 * Get MongoDB connection status
 */
export function getMongoDBConnectionStatus(): {
	isConnected: boolean;
	readyState: number;
	readyStateText: string;
} {
	const readyStateMap = {
		0: 'disconnected',
		1: 'connected',
		2: 'connecting',
		3: 'disconnecting',
	};

	return {
		isConnected,
		readyState: mongoose.connection.readyState,
		readyStateText: readyStateMap[mongoose.connection.readyState as keyof typeof readyStateMap] || 'unknown',
	};
}

/**
 * Health check for MongoDB connection
 */
export async function checkMongoDBHealth(): Promise<'healthy' | 'unhealthy' | 'unknown'> {
	try {
		if (!isConnected || mongoose.connection.readyState !== 1) {
			return 'unhealthy';
		}

		// Perform a simple ping operation
		await mongoose.connection.db.admin().ping();
		return 'healthy';
	} catch (error) {
		errorLogger.error(
			'MongoDB health check failed',
			error instanceof Error ? error : new Error(String(error)),
			createErrorContext('MongoDB', 'health_check'),
			'MongoDB'
		);
		return 'unhealthy';
	}
}

/**
 * Get MongoDB client for direct operations
 */
export function getMongoDBClient() {
	if (!isConnected || mongoose.connection.readyState !== 1) {
		throw new Error('MongoDB is not connected');
	}
	return mongoose.connection.getClient();
}

/**
 * Get MongoDB database instance
 */
export function getMongoDBDatabase() {
	if (!isConnected || mongoose.connection.readyState !== 1) {
		throw new Error('MongoDB is not connected');
	}
	return mongoose.connection.db;
}

// Export mongoose instance for direct use
export { mongoose };
