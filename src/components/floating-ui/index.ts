// ============================================================================
// FLOATING UI EXPORTS
// ============================================================================

// Components
export {
	FloatingUIManager,
	FloatingUIPortal,
	FloatingUIWrapper,
	floatingUIStyles,
} from './floating-ui-manager';

// Context
export {
	FloatingUIProvider,
	useFloatingUI,
} from '@/contexts/floating-ui-context';

// Hooks
export {
	useFloatingUIElement,
	useFloatingSettings,
	useFloatingGuidance,
	useFloatingNotification,
	useFloatingTooltip,
	useFloatingModal,
	useFloatingDropdown,
	useFloatingUIManager,
	useResponsiveFloatingUI,
} from '@/hooks/use-floating-ui';

// Types
export type {
	FloatingUIType,
	FloatingPosition,
	FloatingPriority,
	FloatingCoordinates,
	FloatingDimensions,
	FloatingAnimationConfig,
	FloatingElement,
	FloatingUIState,
	FloatingUIActions,
	FloatingUIContextType,
	FloatingUIConfig,
	UseFloatingUIOptions,
	UseFloatingUIReturn,
	FloatingUIManagerProps,
	FloatingSettingsOptions,
	FloatingGuidanceOptions,
} from '@/types/floating-ui';

export { DEFAULT_FLOATING_CONFIG } from '@/types/floating-ui';
