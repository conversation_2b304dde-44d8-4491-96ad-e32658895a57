const nextJest = require('next/jest');

const createJestConfig = nextJest({
	// Provide the path to your Next.js app to load next.config.js and .env files
	dir: './',
});

// Add any custom config to be passed to Jest
const customJestConfig = {
	// Add more setup options before each test is run
	setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
	
	// Test environment
	testEnvironment: 'node',
	
	// Test patterns for MongoDB tests
	testMatch: [
		'<rootDir>/tests/**/*.mongo.*.test.{js,ts}',
		'<rootDir>/tests/**/mongo/**/*.test.{js,ts}',
	],
	
	// Module name mapping
	moduleNameMapping: {
		'^@/(.*)$': '<rootDir>/src/$1',
	},
	
	// Transform configuration
	transform: {
		'^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { presets: ['next/babel'] }],
	},
	
	// Module file extensions
	moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json'],
	
	// Coverage configuration
	collectCoverageFrom: [
		'src/backend/repositories/**/*.mongo.*.{js,ts}',
		'src/backend/schemas/**/*.{js,ts}',
		'src/config/mongodb.{js,ts}',
		'!src/**/*.d.ts',
		'!src/**/*.test.{js,ts}',
		'!src/**/*.spec.{js,ts}',
	],
	
	// Coverage thresholds
	coverageThreshold: {
		global: {
			branches: 70,
			functions: 70,
			lines: 70,
			statements: 70,
		},
	},
	
	// Test timeout
	testTimeout: 30000,
	
	// Global setup and teardown
	globalSetup: '<rootDir>/tests/setup/mongodb.setup.ts',
	globalTeardown: '<rootDir>/tests/setup/mongodb.setup.ts',
	
	// Setup files
	setupFiles: ['<rootDir>/tests/setup/env.setup.js'],
	
	// Verbose output
	verbose: true,
	
	// Clear mocks between tests
	clearMocks: true,
	
	// Restore mocks after each test
	restoreMocks: true,
	
	// Force exit after tests complete
	forceExit: true,
	
	// Detect open handles
	detectOpenHandles: true,
	
	// Max workers for parallel execution
	maxWorkers: 1, // Use single worker for MongoDB tests to avoid conflicts
};

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig);
